{"version": 3, "file": "span_kind.js", "sourceRoot": "", "sources": ["../../../src/trace/span_kind.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAN,IAAY,QA6BX;AA7BD,WAAY,QAAQ;IAClB,iEAAiE;IACjE,+CAAY,CAAA;IAEZ;;;OAGG;IACH,2CAAU,CAAA;IAEV;;;OAGG;IACH,2CAAU,CAAA;IAEV;;;;OAIG;IACH,+CAAY,CAAA;IAEZ;;;;OAIG;IACH,+CAAY,CAAA;AACd,CAAC,EA7BW,QAAQ,KAAR,QAAQ,QA6BnB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport enum SpanKind {\n  /** Default value. Indicates that the span is used internally. */\n  INTERNAL = 0,\n\n  /**\n   * Indicates that the span covers server-side handling of an RPC or other\n   * remote request.\n   */\n  SERVER = 1,\n\n  /**\n   * Indicates that the span covers the client-side wrapper around an RPC or\n   * other remote request.\n   */\n  CLIENT = 2,\n\n  /**\n   * Indicates that the span describes producer sending a message to a\n   * broker. Unlike client and server, there is no direct critical path latency\n   * relationship between producer and consumer spans.\n   */\n  PRODUCER = 3,\n\n  /**\n   * Indicates that the span describes consumer receiving a message from a\n   * broker. Unlike client and server, there is no direct critical path latency\n   * relationship between producer and consumer spans.\n   */\n  CONSUMER = 4,\n}\n"]}