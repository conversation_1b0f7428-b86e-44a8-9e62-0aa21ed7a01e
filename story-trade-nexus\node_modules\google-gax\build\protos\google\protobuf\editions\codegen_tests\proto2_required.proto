// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

syntax = "proto2";

package protobuf_editions_test.proto2;

message Proto2Required {
  required int32 int32_field = 17;
  required float float_field = 18;
  required double double_field = 19;
  required int64 int64_field = 20;
  required uint32 uint32_field = 21;
  required uint64 uint64_field = 22;
  required sint32 sint32_field = 23;
  required sint64 sint64_field = 24;
  required fixed32 fixed32_field = 25;
  required fixed64 fixed64_field = 26;
  required sfixed32 sfixed32_field = 27;
  required sfixed64 sfixed64_field = 28;
  required bool bool_field = 29;
  required string string_field = 30;
  required bytes bytes_field = 31;

  message SubMessage {
    required int32 int32_field = 17;
    required float float_field = 18;
    required double double_field = 19;
    required int64 int64_field = 20;
    required uint32 uint32_field = 21;
    required uint64 uint64_field = 22;
    required sint32 sint32_field = 23;
    required sint64 sint64_field = 24;
    required fixed32 fixed32_field = 25;
    required fixed64 fixed64_field = 26;
    required sfixed32 sfixed32_field = 27;
    required sfixed64 sfixed64_field = 28;
    required bool bool_field = 29;
    required string string_field = 30;
    required bytes bytes_field = 31;
  }
  required SubMessage sub_message = 2;
}
