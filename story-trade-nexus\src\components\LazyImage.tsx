import React, { useState, useRef, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: React.ReactNode;
  onLoad?: () => void;
  onError?: () => void;
  loading?: 'lazy' | 'eager';
  aspectRatio?: string; // e.g., '16/9', '4/3', '1/1'
  fallbackSrc?: string;
}

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = '',
  style,
  placeholder,
  onLoad,
  onError,
  loading = 'lazy',
  aspectRatio,
  fallbackSrc
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(src);
  const imgRef = useRef<HTMLImageElement>(null);

  // Reset states when src changes
  useEffect(() => {
    setIsLoaded(false);
    setIsError(false);
    setCurrentSrc(src);
  }, [src]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setIsError(true);
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setIsError(false);
    } else {
      onError?.();
    }
  };

  // Default placeholder if none provided
  const defaultPlaceholder = (
    <Skeleton
      className={`w-full h-full ${className}`}
      style={{
        aspectRatio: aspectRatio || 'auto',
        ...style
      }}
    />
  );

  // Error state
  if (isError && (!fallbackSrc || currentSrc === fallbackSrc)) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 text-gray-400 ${className}`}
        style={{
          aspectRatio: aspectRatio || 'auto',
          ...style
        }}
      >
        <div className="text-center p-4">
          <svg
            className="w-8 h-8 mx-auto mb-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <p className="text-xs">Image not available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative" style={{ aspectRatio: aspectRatio || 'auto' }}>
      {/* Show placeholder while loading */}
      {!isLoaded && (placeholder || defaultPlaceholder)}

      {/* Actual image */}
      <img
        ref={imgRef}
        src={currentSrc}
        alt={alt}
        className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
        style={{
          ...style,
          position: isLoaded ? 'static' : 'absolute',
          top: isLoaded ? 'auto' : 0,
          left: isLoaded ? 'auto' : 0,
          width: '100%',
          height: '100%'
        }}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        decoding="async"
      />
    </div>
  );
};

// Higher-order component for book images with specific optimizations
interface BookImageProps extends Omit<LazyImageProps, 'aspectRatio' | 'alt'> {
  book?: {
    title: string;
    author?: string;
  };
  size?: 'small' | 'medium' | 'large';
  alt?: string;
}

export const BookImage: React.FC<BookImageProps> = ({
  book,
  size = 'medium',
  alt,
  className = '',
  ...props
}) => {
  const aspectRatios = {
    small: '3/4',   // Book card aspect ratio
    medium: '3/4',  // Standard book aspect ratio
    large: '3/4'    // Detail page aspect ratio
  };

  const sizeClasses = {
    small: 'h-48',
    medium: 'h-64',
    large: 'h-96'
  };

  const bookAlt = alt || (book ? `${book.title}${book.author ? ` by ${book.author}` : ''}` : 'Book cover');

  return (
    <LazyImage
      {...props}
      alt={bookAlt}
      aspectRatio={aspectRatios[size]}
      className={`${sizeClasses[size]} ${className}`}
      placeholder={
        <div className={`${sizeClasses[size]} bg-gray-100 animate-pulse flex items-center justify-center ${className}`}>
          <div className="text-center text-gray-400">
            <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            <p className="text-xs">Loading...</p>
          </div>
        </div>
      }
    />
  );
};
