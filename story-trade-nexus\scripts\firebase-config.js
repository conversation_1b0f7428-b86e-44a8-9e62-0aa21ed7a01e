/**
 * Firebase Admin SDK Configuration for WebP Migration
 * 
 * This file handles the Firebase Admin SDK initialization for server-side operations.
 * It supports multiple authentication methods for different environments.
 */

import { initializeApp, cert, getApps } from 'firebase-admin/app';
import { getStorage } from 'firebase-admin/storage';
import { getFirestore } from 'firebase-admin/firestore';

// Firebase project configuration
const FIREBASE_CONFIG = {
  projectId: 'book-share-98f6a',
  storageBucket: 'book-share-98f6a.firebasestorage.app'
};

let adminApp = null;
let storage = null;
let firestore = null;

/**
 * Initialize Firebase Admin SDK
 * 
 * This function tries multiple authentication methods:
 * 1. Service Account Key (if GOOGLE_APPLICATION_CREDENTIALS is set)
 * 2. Default credentials (for Google Cloud environments)
 * 3. Application Default Credentials
 */
export async function initializeFirebaseAdmin() {
  try {
    // Check if already initialized
    if (getApps().length > 0) {
      console.log('Firebase Admin already initialized');
      adminApp = getApps()[0];
      storage = getStorage(adminApp);
      firestore = getFirestore(adminApp);
      return { adminApp, storage, firestore };
    }

    console.log('Initializing Firebase Admin SDK...');

    // Method 1: Try with service account key file
    if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
      console.log('Using service account credentials from GOOGLE_APPLICATION_CREDENTIALS');
      
      try {
        const serviceAccount = await import(process.env.GOOGLE_APPLICATION_CREDENTIALS, {
          assert: { type: 'json' }
        });
        
        adminApp = initializeApp({
          credential: cert(serviceAccount.default),
          projectId: FIREBASE_CONFIG.projectId,
          storageBucket: FIREBASE_CONFIG.storageBucket
        });
      } catch (error) {
        console.warn('Failed to load service account from file:', error.message);
        throw error;
      }
    }
    // Method 2: Try with environment variables
    else if (process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL) {
      console.log('Using service account credentials from environment variables');
      
      const serviceAccount = {
        type: 'service_account',
        project_id: FIREBASE_CONFIG.projectId,
        private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
        private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        client_id: process.env.FIREBASE_CLIENT_ID,
        auth_uri: 'https://accounts.google.com/o/oauth2/auth',
        token_uri: 'https://oauth2.googleapis.com/token',
        auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
        client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
      };

      adminApp = initializeApp({
        credential: cert(serviceAccount),
        projectId: FIREBASE_CONFIG.projectId,
        storageBucket: FIREBASE_CONFIG.storageBucket
      });
    }
    // Method 3: Try with default credentials (for Google Cloud environments)
    else {
      console.log('Using default credentials (Google Cloud environment)');
      
      adminApp = initializeApp({
        projectId: FIREBASE_CONFIG.projectId,
        storageBucket: FIREBASE_CONFIG.storageBucket
      });
    }

    // Initialize services
    storage = getStorage(adminApp);
    firestore = getFirestore(adminApp);

    console.log('Firebase Admin SDK initialized successfully');
    console.log(`Project ID: ${FIREBASE_CONFIG.projectId}`);
    console.log(`Storage Bucket: ${FIREBASE_CONFIG.storageBucket}`);

    return { adminApp, storage, firestore };

  } catch (error) {
    console.error('Failed to initialize Firebase Admin SDK:', error.message);
    console.error('\nTo fix this issue, you need to set up authentication:');
    console.error('\nOption 1: Service Account Key File');
    console.error('1. Go to Firebase Console > Project Settings > Service Accounts');
    console.error('2. Click "Generate new private key" and download the JSON file');
    console.error('3. Set environment variable: GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json');
    console.error('\nOption 2: Environment Variables');
    console.error('Set these environment variables from your service account JSON:');
    console.error('- FIREBASE_PRIVATE_KEY');
    console.error('- FIREBASE_CLIENT_EMAIL');
    console.error('- FIREBASE_PRIVATE_KEY_ID (optional)');
    console.error('- FIREBASE_CLIENT_ID (optional)');
    console.error('\nOption 3: Google Cloud Environment');
    console.error('Run this script in a Google Cloud environment with default credentials');
    
    throw error;
  }
}

/**
 * Get initialized Firebase services
 */
export function getFirebaseServices() {
  if (!adminApp || !storage || !firestore) {
    throw new Error('Firebase Admin SDK not initialized. Call initializeFirebaseAdmin() first.');
  }
  
  return { adminApp, storage, firestore };
}

/**
 * Test Firebase connection
 */
export async function testFirebaseConnection() {
  try {
    const { storage, firestore } = getFirebaseServices();
    
    // Test storage access
    const bucket = storage.bucket();
    await bucket.getMetadata();
    console.log('✓ Storage connection successful');
    
    // Test Firestore access
    const testDoc = firestore.collection('_test').doc('connection');
    await testDoc.get();
    console.log('✓ Firestore connection successful');
    
    return true;
  } catch (error) {
    console.error('✗ Firebase connection test failed:', error.message);
    return false;
  }
}

export { FIREBASE_CONFIG };
