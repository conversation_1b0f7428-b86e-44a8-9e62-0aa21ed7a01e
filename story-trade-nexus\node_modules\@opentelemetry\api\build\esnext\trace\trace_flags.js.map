{"version": 3, "file": "trace_flags.js", "sourceRoot": "", "sources": ["../../../src/trace/trace_flags.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAN,IAAY,UAKX;AALD,WAAY,UAAU;IACpB,8BAA8B;IAC9B,2CAAU,CAAA;IACV,gEAAgE;IAChE,iDAAkB,CAAA;AACpB,CAAC,EALW,UAAU,KAAV,UAAU,QAKrB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport enum TraceFlags {\n  /** Represents no flag set. */\n  NONE = 0x0,\n  /** Bit to represent whether trace is sampled in trace flags. */\n  SAMPLED = 0x1 << 0,\n}\n"]}