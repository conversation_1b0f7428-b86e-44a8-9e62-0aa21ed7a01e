{"version": 3, "file": "ProxyTracerProvider.js", "sourceRoot": "", "sources": ["../../../src/trace/ProxyTracerProvider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,+CAA4C;AAC5C,6DAA0D;AAG1D,MAAM,oBAAoB,GAAG,IAAI,uCAAkB,EAAE,CAAC;AAEtD;;;;;;;GAOG;AACH,MAAa,mBAAmB;IAG9B;;OAEG;IACH,SAAS,CAAC,IAAY,EAAE,OAAgB,EAAE,OAAuB;;QAC/D,OAAO,CACL,MAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,mCAC9C,IAAI,yBAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,WAAW;;QACT,OAAO,MAAA,IAAI,CAAC,SAAS,mCAAI,oBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,iBAAiB,CACf,IAAY,EACZ,OAAgB,EAChB,OAAuB;;QAEvB,OAAO,MAAA,IAAI,CAAC,SAAS,0CAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;CACF;AA/BD,kDA+BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Tracer } from './tracer';\nimport { TracerProvider } from './tracer_provider';\nimport { ProxyTracer } from './ProxyTracer';\nimport { NoopTracerProvider } from './NoopTracerProvider';\nimport { TracerOptions } from './tracer_options';\n\nconst NOOP_TRACER_PROVIDER = new NoopTracerProvider();\n\n/**\n * Tracer provider which provides {@link ProxyTracer}s.\n *\n * Before a delegate is set, tracers provided are NoOp.\n *   When a delegate is set, traces are provided from the delegate.\n *   When a delegate is set after tracers have already been provided,\n *   all tracers already provided will use the provided delegate implementation.\n */\nexport class ProxyTracerProvider implements TracerProvider {\n  private _delegate?: TracerProvider;\n\n  /**\n   * Get a {@link ProxyTracer}\n   */\n  getTracer(name: string, version?: string, options?: TracerOptions): Tracer {\n    return (\n      this.getDelegateTracer(name, version, options) ??\n      new ProxyTracer(this, name, version, options)\n    );\n  }\n\n  getDelegate(): TracerProvider {\n    return this._delegate ?? NOOP_TRACER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate tracer provider\n   */\n  setDelegate(delegate: TracerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateTracer(\n    name: string,\n    version?: string,\n    options?: TracerOptions\n  ): Tracer | undefined {\n    return this._delegate?.getTracer(name, version, options);\n  }\n}\n"]}