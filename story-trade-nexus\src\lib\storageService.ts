import { ref, uploadBytes, getDownloadURL, getStorage, deleteObject } from 'firebase/storage';
import { storage } from './firebase';
import { processImage, validateImageFile, supportsWebP } from './imageProcessing';

// Default image URL to use if upload fails
const DEFAULT_IMAGE_URL = "https://via.placeholder.com/150?text=No+Image";

/**
 * Uploads an image to Firebase Storage with automatic WebP conversion and optimization
 * @param imageFile The image file to upload
 * @param path The path in storage where the image should be stored
 * @param onProgress Optional callback for tracking upload progress
 * @param options Optional processing options
 * @returns The download URL of the uploaded image
 */
export const uploadImage = async (
  imageFile: File,
  path: string,
  onProgress?: (progress: number) => void,
  options: {
    skipProcessing?: boolean;
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
  } = {}
): Promise<string> => {
  try {
    console.log(`Uploading image to path: ${path}`);
    console.log(`Image file details: name=${imageFile.name}, size=${imageFile.size}, type=${imageFile.type}`);

    // Validate inputs
    if (!imageFile) {
      console.error('No image file provided');
      return DEFAULT_IMAGE_URL;
    }

    if (!path) {
      console.error('No path provided');
      return DEFAULT_IMAGE_URL;
    }

    // Validate image file
    const validation = validateImageFile(imageFile);
    if (!validation.valid) {
      console.error('Image validation failed:', validation.error);
      return DEFAULT_IMAGE_URL;
    }

    let fileToUpload = imageFile;
    let finalPath = path;

    // Process image unless explicitly skipped
    if (!options.skipProcessing) {
      try {
        console.log('Processing image for optimization...');
        if (onProgress) onProgress(10);

        const processed = await processImage(imageFile, {
          maxWidth: options.maxWidth,
          maxHeight: options.maxHeight,
          quality: options.quality
        });

        fileToUpload = processed.file;

        // Update path to reflect new format if changed
        if (processed.format !== imageFile.type.split('/')[1]) {
          finalPath = path.replace(/\.[^.]+$/, `.${processed.format}`);
        }

        console.log(`Image processed: ${imageFile.size} bytes -> ${processed.file.size} bytes (${processed.format})`);
        if (onProgress) onProgress(30);
      } catch (processingError) {
        console.warn('Image processing failed, uploading original:', processingError);
        // Continue with original file if processing fails
      }
    }

    // Create a storage reference
    const storageRef = ref(storage, finalPath);
    console.log(`Storage reference created: ${storageRef.fullPath}`);

    // Set metadata with content type
    const metadata = {
      contentType: fileToUpload.type,
      customMetadata: {
        originalName: imageFile.name,
        originalSize: imageFile.size.toString(),
        processedSize: fileToUpload.size.toString(),
        uploadedAt: new Date().toISOString()
      }
    };
    console.log('Metadata:', metadata);

    // Upload the file
    console.log('Starting file upload...');
    if (onProgress) onProgress(50);

    try {
      const snapshot = await uploadBytes(storageRef, fileToUpload, metadata);
      console.log('Image uploaded successfully');
      console.log('Upload snapshot:', {
        bytesTransferred: snapshot.bytesTransferred,
        totalBytes: snapshot.totalBytes,
        state: snapshot.state
      });

      if (onProgress) onProgress(90);

      // Get the download URL
      console.log('Getting download URL...');
      const downloadURL = await getDownloadURL(snapshot.ref);
      console.log('Image download URL:', downloadURL);

      // Call progress callback with 100% when complete
      if (onProgress) {
        console.log('Calling progress callback with 100%');
        onProgress(100);
      }

      return downloadURL;
    } catch (uploadError) {
      console.error('Error in uploadBytes:', uploadError);
      if (uploadError instanceof Error) {
        console.error('Upload error details:', {
          message: uploadError.message,
          stack: uploadError.stack
        });
      }
      throw uploadError; // Re-throw to be caught by the outer try-catch
    }
  } catch (error) {
    console.error('Error uploading image:', error);
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack
      });
    }
    // Return default image URL instead of throwing
    return DEFAULT_IMAGE_URL;
  }
};

/**
 * Uploads multiple images to Firebase Storage
 * @param imageFiles Array of image files to upload
 * @param userId User ID for path generation
 * @param onProgress Optional callback for tracking overall upload progress
 * @returns Array of download URLs for the uploaded images
 */
export const uploadMultipleImages = async (
  imageFiles: File[],
  userId: string,
  onProgress?: (progress: number) => void
): Promise<string[]> => {
  try {
    console.log(`Uploading ${imageFiles.length} images`);
    console.log('Image files:', imageFiles.map(f => ({ name: f.name, size: f.size, type: f.type })));
    console.log('User ID:', userId);

    // Validate inputs
    if (!imageFiles || imageFiles.length === 0) {
      console.error('No image files provided');
      return [DEFAULT_IMAGE_URL];
    }

    if (!userId) {
      console.error('No user ID provided');
      return [DEFAULT_IMAGE_URL];
    }

    const uploadPromises: Promise<string>[] = [];
    const totalFiles = imageFiles.length;
    let completedFiles = 0;

    // Create upload promises for each file
    for (const file of imageFiles) {
      console.log(`Processing file: ${file.name} (${file.size} bytes)`);
      const path = generateImagePathSync(userId, file.name); // Use sync version for simplicity
      console.log(`Generated path: ${path}`);

      const uploadPromise = uploadImage(file, path, (fileProgress) => {
        console.log(`File ${file.name} progress: ${fileProgress}%`);
        // If a file is 100% complete, increment completed files
        if (fileProgress === 100) {
          completedFiles++;

          // Calculate overall progress
          if (onProgress) {
            const overallProgress = Math.round((completedFiles / totalFiles) * 100);
            console.log(`Overall progress: ${overallProgress}%`);
            onProgress(overallProgress);
          }
        }
      });

      uploadPromises.push(uploadPromise);
    }

    console.log(`Created ${uploadPromises.length} upload promises`);

    // Wait for all uploads to complete
    const urls = await Promise.all(uploadPromises);
    console.log(`All uploads completed. Results:`, urls);

    // Filter out any default URLs (failed uploads)
    const successfulUrls = urls.filter(url => url !== DEFAULT_IMAGE_URL);
    console.log(`Successful uploads: ${successfulUrls.length}/${urls.length}`);

    // If all uploads failed, return at least the default URL
    if (successfulUrls.length === 0 && urls.length > 0) {
      console.log('All uploads failed, returning default URL');
      return [DEFAULT_IMAGE_URL];
    }

    console.log('Final URLs:', successfulUrls);
    return successfulUrls;
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack
      });
    }
    // Return array with default image URL
    return [DEFAULT_IMAGE_URL];
  }
};

/**
 * Deletes an image from Firebase Storage
 * @param imageUrl The URL of the image to delete
 * @returns True if deletion was successful, false otherwise
 */
export const deleteImage = async (imageUrl: string): Promise<boolean> => {
  try {
    // Skip deletion if it's the default image
    if (imageUrl === DEFAULT_IMAGE_URL) {
      console.log('Skipping deletion of default image');
      return true;
    }

    // Extract the path from the URL
    const path = getPathFromUrl(imageUrl);
    if (!path) {
      console.error('Could not extract path from URL:', imageUrl);
      return false;
    }

    console.log(`Deleting image at path: ${path}`);

    // Create a reference to the file
    const imageRef = ref(storage, path);

    // Delete the file
    await deleteObject(imageRef);
    console.log('Image deleted successfully');

    return true;
  } catch (error) {
    console.error('Error deleting image:', error);
    return false;
  }
};

/**
 * Extracts the storage path from a Firebase Storage URL
 * @param url The Firebase Storage URL
 * @returns The path in the storage bucket, or null if extraction failed
 */
const getPathFromUrl = (url: string): string | null => {
  try {
    // Extract the path from the URL
    // URLs are typically in the format: https://firebasestorage.googleapis.com/v0/b/BUCKET_NAME/o/PATH?token=TOKEN
    const pathRegex = /\/o\/([^?]+)/;
    const match = url.match(pathRegex);

    if (match && match[1]) {
      // Decode the URL-encoded path
      return decodeURIComponent(match[1]);
    }

    return null;
  } catch (error) {
    console.error('Error extracting path from URL:', error);
    return null;
  }
};

/**
 * Generates a unique path for an image in Firebase Storage
 * @param userId The ID of the user uploading the image
 * @param fileName The original filename of the image
 * @param forceWebP Whether to force WebP extension (default: auto-detect browser support)
 * @returns A unique path string
 */
export const generateImagePath = async (userId: string, fileName: string, forceWebP?: boolean): Promise<string> => {
  const timestamp = new Date().getTime();
  const randomString = Math.random().toString(36).substring(2, 6);

  // Determine file extension
  let fileExtension = 'webp'; // Default to WebP

  if (forceWebP === false) {
    // Use original extension if WebP is explicitly disabled
    fileExtension = fileName.split('.').pop() || 'jpg';
  } else if (forceWebP === undefined) {
    // Auto-detect browser support
    const webpSupported = await supportsWebP();
    fileExtension = webpSupported ? 'webp' : (fileName.split('.').pop() || 'jpg');
  }

  // Create a path structure with user ID
  return `book-images/${userId}/${timestamp}-${randomString}.${fileExtension}`;
};

/**
 * Synchronous version of generateImagePath for backward compatibility
 * @param userId The ID of the user uploading the image
 * @param fileName The original filename of the image
 * @returns A unique path string with WebP extension
 */
export const generateImagePathSync = (userId: string, fileName: string): string => {
  const timestamp = new Date().getTime();
  const randomString = Math.random().toString(36).substring(2, 6);

  // Default to WebP extension for new uploads
  const fileExtension = 'webp';

  // Create a path structure with user ID
  return `book-images/${userId}/${timestamp}-${randomString}.${fileExtension}`;
};

/**
 * Gets a reference to the Firebase Storage bucket
 * @returns The storage bucket reference
 */
export const getStorageBucket = () => {
  return storage;
};
