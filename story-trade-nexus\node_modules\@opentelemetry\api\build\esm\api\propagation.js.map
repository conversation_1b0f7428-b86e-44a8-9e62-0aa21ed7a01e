{"version": 3, "file": "propagation.js", "sourceRoot": "", "sources": ["../../../src/api/propagation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EACL,SAAS,EACT,cAAc,EACd,gBAAgB,GACjB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,qBAAqB,EAAE,MAAM,sCAAsC,CAAC;AAC7E,OAAO,EACL,oBAAoB,EACpB,oBAAoB,GAIrB,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EACL,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,GACd,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AAEjC,IAAM,QAAQ,GAAG,aAAa,CAAC;AAC/B,IAAM,wBAAwB,GAAG,IAAI,qBAAqB,EAAE,CAAC;AAE7D;;GAEG;AACH;IAGE,+FAA+F;IAC/F;QA8DO,kBAAa,GAAG,aAAa,CAAC;QAE9B,eAAU,GAAG,UAAU,CAAC;QAExB,qBAAgB,GAAG,gBAAgB,CAAC;QAEpC,eAAU,GAAG,UAAU,CAAC;QAExB,kBAAa,GAAG,aAAa,CAAC;IAtEd,CAAC;IAExB,uDAAuD;IACzC,0BAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,EAAE,CAAC;SACvC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,4CAAmB,GAA1B,UAA2B,UAA6B;QACtD,OAAO,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;OAMG;IACI,+BAAM,GAAb,UACE,OAAgB,EAChB,OAAgB,EAChB,MAAqD;QAArD,uBAAA,EAAA,6BAAqD;QAErD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;OAMG;IACI,gCAAO,GAAd,UACE,OAAgB,EAChB,OAAgB,EAChB,MAAqD;QAArD,uBAAA,EAAA,6BAAqD;QAErD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACI,+BAAM,GAAb;QACE,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,CAAC;IAC9C,CAAC;IAED,mCAAmC;IAC5B,gCAAO,GAAd;QACE,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;IAYO,6CAAoB,GAA5B;QACE,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,wBAAwB,CAAC;IACzD,CAAC;IACH,qBAAC;AAAD,CAAC,AA/ED,IA+EC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\nimport { NoopTextMapPropagator } from '../propagation/NoopTextMapPropagator';\nimport {\n  defaultTextMapGetter,\n  defaultTextMapSetter,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n} from '../propagation/TextMapPropagator';\nimport {\n  getBaggage,\n  getActiveBaggage,\n  setBaggage,\n  deleteBaggage,\n} from '../baggage/context-helpers';\nimport { createBaggage } from '../baggage/utils';\nimport { DiagAPI } from './diag';\n\nconst API_NAME = 'propagation';\nconst NOOP_TEXT_MAP_PROPAGATOR = new NoopTextMapPropagator();\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Propagation API\n */\nexport class PropagationAPI {\n  private static _instance?: PropagationAPI;\n\n  /** Empty private constructor prevents end users from constructing a new instance of the API */\n  private constructor() {}\n\n  /** Get the singleton instance of the Propagator API */\n  public static getInstance(): PropagationAPI {\n    if (!this._instance) {\n      this._instance = new PropagationAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Set the current propagator.\n   *\n   * @returns true if the propagator was successfully registered, else false\n   */\n  public setGlobalPropagator(propagator: TextMapPropagator): boolean {\n    return registerGlobal(API_NAME, propagator, DiagAPI.instance());\n  }\n\n  /**\n   * Inject context into a carrier to be propagated inter-process\n   *\n   * @param context Context carrying tracing data to inject\n   * @param carrier carrier to inject context into\n   * @param setter Function used to set values on the carrier\n   */\n  public inject<Carrier>(\n    context: Context,\n    carrier: Carrier,\n    setter: TextMapSetter<Carrier> = defaultTextMapSetter\n  ): void {\n    return this._getGlobalPropagator().inject(context, carrier, setter);\n  }\n\n  /**\n   * Extract context from a carrier\n   *\n   * @param context Context which the newly created context will inherit from\n   * @param carrier Carrier to extract context from\n   * @param getter Function used to extract keys from a carrier\n   */\n  public extract<Carrier>(\n    context: Context,\n    carrier: Carrier,\n    getter: TextMapGetter<Carrier> = defaultTextMapGetter\n  ): Context {\n    return this._getGlobalPropagator().extract(context, carrier, getter);\n  }\n\n  /**\n   * Return a list of all fields which may be used by the propagator.\n   */\n  public fields(): string[] {\n    return this._getGlobalPropagator().fields();\n  }\n\n  /** Remove the global propagator */\n  public disable() {\n    unregisterGlobal(API_NAME, DiagAPI.instance());\n  }\n\n  public createBaggage = createBaggage;\n\n  public getBaggage = getBaggage;\n\n  public getActiveBaggage = getActiveBaggage;\n\n  public setBaggage = setBaggage;\n\n  public deleteBaggage = deleteBaggage;\n\n  private _getGlobalPropagator(): TextMapPropagator {\n    return getGlobal(API_NAME) || NOOP_TEXT_MAP_PROPAGATOR;\n  }\n}\n"]}