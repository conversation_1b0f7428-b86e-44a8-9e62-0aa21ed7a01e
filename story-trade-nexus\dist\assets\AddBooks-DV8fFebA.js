const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-ehpEbksy.js","assets/index.esm2017-H7c5Bkvh.js","assets/index-Dkj_9ZOH.js","assets/index-Dzh6PD-A.css"])))=>i.map(i=>d[i]);
import{p as re,ay as ie,z as x,E as le,r as F,u as ne,q as ae,j as e,H as ce,t as de,v as f,w as b,x as y,y as j,I as L,A as N,O,X as ue,h as me,a as H,n as ge,o as pe,J as v,D as he,_ as Y,G as xe}from"./index-Dkj_9ZOH.js";import{T as fe}from"./textarea-BfoITkEs.js";import{ref as be,uploadBytes as ye,getDownloadURL as je}from"./index.esm-BEM7nnuS.js";import{getCurrentPosition as ve}from"./geolocationUtils-DGx5pdrF.js";import{C as we}from"./circle-alert-DCsrYdzX.js";import"./index.esm2017-H7c5Bkvh.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=re("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),U="https://via.placeholder.com/150?text=No+Image",ke=async(n,r,u)=>{try{if(console.log(`Uploading image to path: ${r}`),console.log(`Image file details: name=${n.name}, size=${n.size}, type=${n.type}`),!n)return console.error("No image file provided"),U;if(!r)return console.error("No path provided"),U;const s=be(ie,r);console.log(`Storage reference created: ${s.fullPath}`),console.log(`Storage bucket: ${s.bucket}`);const I={contentType:n.type};console.log("Metadata:",I),console.log("Starting file upload...");try{const a=await ye(s,n,I);console.log("Image uploaded successfully"),console.log("Upload snapshot:",{bytesTransferred:a.bytesTransferred,totalBytes:a.totalBytes,state:a.state}),u&&(console.log("Calling progress callback with 100%"),u(100)),console.log("Getting download URL...");const k=await je(a.ref);return console.log("Image download URL:",k),k}catch(a){throw console.error("Error in uploadBytes:",a),a instanceof Error&&console.error("Upload error details:",{message:a.message,stack:a.stack}),a}}catch(s){return console.error("Error uploading image:",s),s instanceof Error&&console.error("Error details:",{message:s.message,stack:s.stack}),U}},Se=async(n,r,u)=>{try{if(console.log(`Uploading ${n.length} images`),console.log("Image files:",n.map(c=>({name:c.name,size:c.size,type:c.type}))),console.log("User ID:",r),!n||n.length===0)return console.error("No image files provided"),[U];if(!r)return console.error("No user ID provided"),[U];const s=[],I=n.length;let a=0;for(const c of n){console.log(`Processing file: ${c.name} (${c.size} bytes)`);const C=Ee(r,c.name);console.log(`Generated path: ${C}`);const $=ke(c,C,B=>{if(console.log(`File ${c.name} progress: ${B}%`),B===100&&(a++,u)){const A=Math.round(a/I*100);console.log(`Overall progress: ${A}%`),u(A)}});s.push($)}console.log(`Created ${s.length} upload promises`);const k=await Promise.all(s);console.log("All uploads completed. Results:",k);const g=k.filter(c=>c!==U);return console.log(`Successful uploads: ${g.length}/${k.length}`),g.length===0&&k.length>0?(console.log("All uploads failed, returning default URL"),[U]):(console.log("Final URLs:",g),g)}catch(s){return console.error("Error uploading multiple images:",s),s instanceof Error&&console.error("Error details:",{message:s.message,stack:s.stack}),[U]}},Ee=(n,r)=>{const u=new Date().getTime(),s=Math.random().toString(36).substring(2,6),I=r.split(".").pop()||"jpg";return`book-images/${n}/${u}-${s}.${I}`},Ie=x.object({title:x.string().min(1,{message:"Book title is required"}),author:x.string().min(1,{message:"Author name is required"}),isbn:x.string().optional(),genre:x.string().min(1,{message:"Please select at least one genre"}),condition:x.string().min(1,{message:"Please select a condition"}),description:x.string().min(10,{message:"Description should be at least 10 characters"}),availability:x.string().min(1,{message:"Please select availability option"}),price:x.string().optional(),rentalPrice:x.string().optional(),rentalPeriod:x.string().optional(),securityDepositRequired:x.boolean().optional().default(!1),securityDepositAmount:x.string().optional()}),Le=()=>{const n=le(),[r,u]=F.useState(!1);F.useState("");const[s,I]=F.useState([]),[a,k]=F.useState([]),[g,c]=F.useState(0),[C,$]=F.useState(0),[B,A]=F.useState(!1),[G,T]=F.useState(null),[V,J]=F.useState(null),{currentUser:p,userData:P}=ne(),W=o=>{const t=o.target.files;if(!t)return;if(s.length+t.length>4){v.error("Maximum 4 images allowed");return}const h=Array.from(t),m=[...s,...h];I(m);const l=h.map(E=>URL.createObjectURL(E)),S=[...a,...l];k(S),s.length===0&&h.length>0&&c(0)},X=o=>{const t=[...s],h=[...a];t.splice(o,1),h.splice(o,1),I(t),k(h),o===g?(t.length>0,c(0)):o<g&&c(g-1)},K=o=>{c(o)},Q=async()=>{A(!0),T(null);try{console.log("Attempting to capture GPS location...");const o=await ve({enableHighAccuracy:!0,timeout:15e3,maximumAge:6e4});return console.log("GPS location captured successfully:",o),J(o),o}catch(o){console.error("Error capturing GPS location:",o);let t="Unable to get your current location.";return o instanceof Error&&(o.message.includes("permission")?t="Location permission denied. Using your registered address instead.":o.message.includes("timeout")?t="Location request timed out. Using your registered address instead.":o.message.includes("unavailable")&&(t="Location service unavailable. Using your registered address instead.")),T(t),null}finally{A(!1)}},Z=async()=>{if(!(P!=null&&P.pincode))return console.log("No pincode available for fallback location"),null;try{const t={500001:{latitude:17.385,longitude:78.4867},500032:{latitude:17.4399,longitude:78.3489},500081:{latitude:17.4485,longitude:78.3908},400001:{latitude:18.9322,longitude:72.8264},400051:{latitude:19.0596,longitude:72.8295},110001:{latitude:28.6139,longitude:77.209},110016:{latitude:28.5494,longitude:77.2001},560001:{latitude:12.9716,longitude:77.5946},560066:{latitude:12.9698,longitude:77.75},600001:{latitude:13.0827,longitude:80.2707},600028:{latitude:13.0569,longitude:80.2091}}[P.pincode];if(t)return console.log("Fallback location from pincode mapping:",t),t;const h=P.pincode.substring(0,2),l={50:{latitude:17.385,longitude:78.4867},40:{latitude:19.076,longitude:72.8777},11:{latitude:28.7041,longitude:77.1025},56:{latitude:12.9716,longitude:77.5946},60:{latitude:13.0827,longitude:80.2707},70:{latitude:22.5726,longitude:88.3639},30:{latitude:26.9124,longitude:75.7873},22:{latitude:26.8467,longitude:80.9462}}[h];return l?(console.log("Fallback location from state mapping:",l),l):(console.log("No fallback location available for pincode:",P.pincode),null)}catch(o){return console.error("Error getting fallback location from pincode:",o),null}},ee=async()=>{if(!(p!=null&&p.uid))return console.log("No current user available for community retrieval"),null;try{console.log("Retrieving user community from Firestore..."),await xe();const{doc:o,getDoc:t,getFirestore:h}=await Y(async()=>{const{doc:E,getDoc:w,getFirestore:D}=await import("./index.esm-ehpEbksy.js");return{doc:E,getDoc:w,getFirestore:D}},__vite__mapDeps([0,1])),m=h(),l=o(m,"users",p.uid),S=await t(l);if(S.exists()){const w=S.data().community;return w&&typeof w=="string"&&w.trim()!==""?(console.log("User community retrieved successfully:",w),w.trim()):(console.log("User community is empty or not set"),null)}else return console.log("User document not found in Firestore"),null}catch(o){return console.error("Error retrieving user community:",o),null}},i=ae({resolver:he(Ie),defaultValues:{title:"",author:"",isbn:"",genre:"",condition:"",description:"",availability:"",price:"",rentalPrice:"",rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:""}}),oe=async o=>{var t,h;u(!0);try{console.log("Adding book:",o);const{createBook:m}=await Y(async()=>{const{createBook:d}=await import("./index-Dkj_9ZOH.js").then(se=>se.b8);return{createBook:d}},__vite__mapDeps([2,3]));if(!p){v.error("You must be signed in to add a book"),u(!1);return}console.log("Capturing location for book listing...");let l=null;l=await Q(),l||(console.log("GPS capture failed, trying fallback location..."),l=await Z()),l?console.log("Location captured for book:",l):console.log("No location could be determined for book"),console.log("Retrieving user community for book listing...");let S=null;try{S=await ee(),S?(console.log("✅ User community retrieved successfully for book:",S),v.info(`Community "${S}" will be added to your book listing`)):(console.warn("⚠️ No community information available for user - book will be created without community data"),v.warning("No community information found in your profile. Consider updating your profile to help others find books in your area."))}catch(d){console.error("❌ Error retrieving user community:",d),console.error("Community error details:",{message:d.message,stack:d.stack,currentUser:p==null?void 0:p.uid,timestamp:new Date().toISOString()}),v.error("Failed to retrieve community information. Your book will be listed without community data.")}let E=[];try{E=o.genre.split(",").map(d=>d.trim()).filter(d=>d.length>0),E.length===0&&(E=[o.genre.trim()])}catch(d){console.error("Error processing genre:",d),E=[o.genre.trim()]}let w=null;if(o.price&&(w=Number(o.price),isNaN(w))){v.error("Invalid price value. Please enter a valid number."),u(!1);return}let D=null;if(o.rentalPrice&&(D=Number(o.rentalPrice),isNaN(D))){v.error("Invalid rental price value. Please enter a valid number."),u(!1);return}let _=o.rentalPeriod;D||(_=null);let q=null;if(o.securityDepositRequired&&o.securityDepositAmount&&(q=Number(o.securityDepositAmount),isNaN(q))){v.error("Invalid security deposit amount. Please enter a valid number."),u(!1);return}let R=[],M="https://via.placeholder.com/150?text=No+Image";if(s.length>0)try{v.info("Uploading images..."),R=await Se(s,p.uid,d=>{$(d),console.log(`Upload progress: ${d}%`)}),console.log("Uploaded image URLs:",R),R.length>0&&(M=R[g]||R[0])}catch(d){console.error("Error uploading images:",d),v.error("Failed to upload images. Using default image instead.")}const z={title:o.title.trim(),author:o.author.trim(),isbn:((t=o.isbn)==null?void 0:t.trim())||null,genre:E,condition:o.condition,description:o.description.trim(),imageUrl:M,imageUrls:R.length>0?R:void 0,displayImageIndex:R.length>0?g:void 0,availability:o.availability,price:w,rentalPrice:D,rentalPeriod:_,securityDepositRequired:o.securityDepositRequired,securityDepositAmount:q,ownerId:p.uid,ownerName:p.displayName||((h=p.email)==null?void 0:h.split("@")[0])||"Unknown",ownerEmail:p.email||void 0,ownerCommunity:S||void 0,ownerCoordinates:l,ownerPincode:(P==null?void 0:P.pincode)||void 0,ownerRating:0,perceivedValue:5};console.log("Prepared book data:",z);const te=await m(z);console.log("Book created successfully with ID:",te),v.success("Book added successfully! It will be visible after admin approval."),n("/browse")}catch(m){console.error("Error adding book:",m);let l="Failed to add book. Please try again.";m instanceof Error&&(l=`Error: ${m.message}`,console.error("Error details:",m.message),m.message.includes("permission-denied")?l="You don't have permission to add books. Please check your account.":m.message.includes("network")?l="Network error. Please check your internet connection and try again.":m.message.includes("quota-exceeded")&&(l="Database quota exceeded. Please try again later.")),v.error(l)}finally{u(!1),$(0)}};return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(ce,{}),e.jsx("main",{className:"flex-grow",children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-2xl",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Add Your Book"}),e.jsx("p",{className:"text-gray-600",children:"Share your book with the community"})]}),e.jsx(de,{...i,children:e.jsxs("form",{onSubmit:i.handleSubmit(oe),className:"space-y-6",children:[e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx(f,{control:i.control,name:"title",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Book Title*"}),e.jsx(j,{children:e.jsx(L,{placeholder:"Enter book title",disabled:r,...o})}),e.jsx(N,{})]})}),e.jsx(f,{control:i.control,name:"author",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Author*"}),e.jsx(j,{children:e.jsx(L,{placeholder:"Enter author name",disabled:r,...o})}),e.jsx(N,{})]})}),e.jsx(f,{control:i.control,name:"isbn",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"ISBN (Optional)"}),e.jsx(j,{children:e.jsx(L,{placeholder:"Enter ISBN",disabled:r,...o})}),e.jsx(N,{})]})}),e.jsx(f,{control:i.control,name:"genre",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Genre*"}),e.jsx(j,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:r,...o,children:[e.jsx("option",{value:"",children:"Select Genre"}),e.jsx("option",{value:"Fiction",children:"Fiction"}),e.jsx("option",{value:"Non-Fiction",children:"Non-Fiction"}),e.jsx("option",{value:"Classics",children:"Classics"}),e.jsx("option",{value:"Fantasy",children:"Fantasy"}),e.jsx("option",{value:"Mystery",children:"Mystery"}),e.jsx("option",{value:"Romance",children:"Romance"}),e.jsx("option",{value:"Science Fiction",children:"Science Fiction"}),e.jsx("option",{value:"History",children:"History"})]})}),e.jsx(N,{})]})}),e.jsx(f,{control:i.control,name:"condition",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Condition*"}),e.jsx(j,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:r,...o,children:[e.jsx("option",{value:"",children:"Select Condition"}),e.jsx("option",{value:"New",children:"New"}),e.jsx("option",{value:"Like New",children:"Like New"}),e.jsx("option",{value:"Good",children:"Good"}),e.jsx("option",{value:"Fair",children:"Fair"})]})}),e.jsx(N,{})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Book Images"}),e.jsx("p",{className:"text-sm text-gray-500 mb-2 text-center",children:"Upload up to 4 images of your book. The first image will be the display image."}),e.jsxs("div",{className:"flex items-center justify-center mb-2",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 mr-2",children:[s.length,"/4 images"]}),C>0&&C<100&&e.jsx("div",{className:"w-24 h-2 bg-gray-200 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-burgundy-500",style:{width:`${C}%`}})})]})]}),a.length>0&&e.jsx("div",{className:"grid grid-cols-2 gap-4 mb-4",children:a.map((o,t)=>e.jsxs("div",{className:`relative border rounded-md overflow-hidden ${t===g?"ring-2 ring-burgundy-500":""}`,children:[e.jsx("img",{src:o,alt:`Book image ${t+1}`,className:"w-full h-32 object-contain"}),e.jsxs("div",{className:"absolute top-0 right-0 p-1 flex space-x-1",children:[t!==g&&e.jsx("button",{type:"button",className:"bg-burgundy-500 text-white p-1 rounded-full hover:bg-burgundy-600",onClick:()=>K(t),title:"Set as display image",children:e.jsx(O,{className:"h-4 w-4"})}),e.jsx("button",{type:"button",className:"bg-gray-700 text-white p-1 rounded-full hover:bg-gray-800",onClick:()=>X(t),title:"Remove image",children:e.jsx(ue,{className:"h-4 w-4"})})]}),t===g&&e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-burgundy-500 text-white text-xs py-1 text-center",children:"Display Image"})]},t))}),s.length<4&&e.jsxs("div",{className:"flex justify-center",children:[e.jsx("input",{type:"file",id:"image-upload",className:"hidden",accept:"image/*",multiple:s.length<3,onChange:W,disabled:r}),e.jsxs("label",{htmlFor:"image-upload",className:`flex items-center justify-center text-sm bg-burgundy-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-burgundy-600 ${r?"opacity-50 cursor-not-allowed":""}`,children:[e.jsx(Ne,{className:"h-4 w-4 mr-2"}),s.length===0?"Upload Images":"Add More Images"]})]})]}),e.jsx(f,{control:i.control,name:"availability",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Availability*"}),e.jsx(j,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:r,onChange:t=>{o.onChange(t)},value:o.value,children:[e.jsx("option",{value:"",children:"Select Availability"}),e.jsx("option",{value:"For Exchange",children:"For Exchange"}),e.jsx("option",{value:"For Sale",children:"For Sale"}),e.jsx("option",{value:"For Rent",children:"For Rent"}),e.jsx("option",{value:"For Sale & Exchange",children:"For Sale & Exchange"}),e.jsx("option",{value:"For Rent & Exchange",children:"For Rent & Exchange"}),e.jsx("option",{value:"For Rent & Sale",children:"For Rent & Sale"}),e.jsx("option",{value:"For Rent, Sale & Exchange",children:"For Rent, Sale & Exchange"})]})}),e.jsx(N,{})]})}),i.watch("availability")&&(i.watch("availability").includes("Sale")||i.watch("availability").includes("Rent"))&&e.jsxs("div",{className:"space-y-6",children:[i.watch("availability").includes("Sale")&&e.jsx(f,{control:i.control,name:"price",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Sale Price (₹)"}),e.jsx(j,{children:e.jsx(L,{type:"number",placeholder:"Enter price",disabled:r,...o})}),e.jsx(N,{})]})}),i.watch("availability").includes("Rent")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(f,{control:i.control,name:"rentalPrice",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Rental Price (₹)"}),e.jsx(j,{children:e.jsx(L,{type:"number",placeholder:"Enter rental price",disabled:r,...o})}),e.jsx(N,{})]})}),e.jsx(f,{control:i.control,name:"rentalPeriod",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Period"}),e.jsx(j,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:r,...o,children:[e.jsx("option",{value:"per day",children:"Per Day"}),e.jsx("option",{value:"per week",children:"Per Week"}),e.jsx("option",{value:"per month",children:"Per Month"})]})}),e.jsx(N,{})]})})]}),e.jsx("div",{className:"mt-4",children:e.jsx(f,{control:i.control,name:"securityDepositRequired",render:({field:o})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(j,{children:e.jsx("input",{type:"checkbox",className:"h-4 w-4 mt-1",checked:o.value,onChange:t=>o.onChange(t.target.checked),disabled:r})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(y,{children:"Security Deposit Required"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Require a security deposit for renting this book"})]})]})})}),i.watch("securityDepositRequired")&&e.jsx("div",{className:"mt-4",children:e.jsx(f,{control:i.control,name:"securityDepositAmount",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Security Deposit Amount (₹)"}),e.jsx(j,{children:e.jsx(L,{type:"number",placeholder:"Enter security deposit amount",disabled:r,...o})}),e.jsx(N,{})]})})})]})]})]})]}),e.jsx(f,{control:i.control,name:"description",render:({field:o})=>e.jsxs(b,{children:[e.jsx(y,{children:"Description*"}),e.jsx(j,{children:e.jsx(fe,{placeholder:"Describe the book, its condition, and any other details...",className:"min-h-[120px]",disabled:r,...o})}),e.jsx(N,{})]})}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(me,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Location Information"}),e.jsx("p",{className:"text-sm text-blue-800 mb-2",children:"When you submit this book listing, we will automatically capture your current GPS location to help other users find books near them. This location data will be used solely for:"}),e.jsxs("ul",{className:"text-sm text-blue-800 list-disc list-inside space-y-1 mb-3",children:[e.jsx("li",{children:"Calculating and displaying distance to other users browsing books"}),e.jsx("li",{children:"Helping users find books in their local area"}),e.jsx("li",{children:"Improving the book discovery experience"})]}),e.jsx("p",{className:"text-sm text-blue-800",children:"If GPS location cannot be accessed, we'll use your registered address as a fallback. Your exact location will not be displayed to other users - only the calculated distance."}),B&&e.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-blue-700",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),e.jsx("span",{children:"Capturing your location..."})]}),V&&e.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-green-700",children:[e.jsx(O,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{children:"Location captured successfully"})]}),G&&e.jsxs("div",{className:"mt-3 flex items-start space-x-2 text-sm text-amber-700",children:[e.jsx(we,{className:"h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:G})]})]})]})}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(H,{type:"button",variant:"outline",disabled:r,onClick:()=>n("/"),children:"Cancel"}),e.jsxs(H,{type:"submit",disabled:r,className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-4 w-4"}),"Add Book"]})]})]})})]})})}),e.jsx(pe,{})]})};export{Le as default};
