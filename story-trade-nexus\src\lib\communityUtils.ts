/**
 * Utility functions for fetching and managing community data
 */
import { initializeFirebase } from './firebase';
import { debounce } from './utils';

// Define the community data structure
export interface CommunityData {
  communityName: string;
  address?: string;
  pincode: number | string;
  location?: {
    latitude: number;
    longitude: number;
  };
}

/**
 * Fetch communities from Firestore based on pincode
 * @param pincode - The pincode to filter communities by
 * @param limit - Maximum number of communities to fetch (default: 100)
 * @returns Array of community names
 */
export const fetchCommunitiesByPincode = async (
  pincode: string,
  limit: number = 100
): Promise<string[]> => {
  if (!pincode || pincode.length < 6) {
    return [];
  }

  try {
    // Initialize Firebase if not already initialized
    await initializeFirebase();

    // Dynamically import Firestore functions
    const {
      collection,
      query,
      where,
      limit: limitQuery,
      getDocs,
      getFirestore
    } = await import('firebase/firestore');

    const db = getFirestore();

    console.log(`Fetching communities for pincode: ${pincode}`);

    // Convert pincode to number for query (try both string and number formats)
    const pincodeNumber = parseInt(pincode, 10);

    // Create queries for both string and number formats
    const queries = [
      // Query for pincode as number
      query(
        collection(db, 'hyderabadProperties'),
        where('pincode', '==', pincodeNumber),
        limitQuery(limit)
      ),
      // Query for pincode as string
      query(
        collection(db, 'hyderabadProperties'),
        where('pincode', '==', pincode),
        limitQuery(limit)
      )
    ];

    const communities = new Set<string>(); // Use Set to automatically handle duplicates
    let totalDocuments = 0;

    // Execute both queries to handle different data formats
    for (let i = 0; i < queries.length; i++) {
      const queryType = i === 0 ? 'number' : 'string';
      console.log(`Executing query ${i + 1}/2 (pincode as ${queryType})...`);

      try {
        const querySnapshot = await getDocs(queries[i]);
        const docsFound = querySnapshot.size;
        totalDocuments += docsFound;

        if (docsFound > 0) {
          console.log(`Found ${docsFound} documents with pincode as ${queryType}`);

          querySnapshot.forEach((doc) => {
            const data = doc.data() as CommunityData;
            if (data.communityName && typeof data.communityName === 'string') {
              communities.add(data.communityName.trim());
            }
          });
        } else {
          console.log(`No documents found with pincode as ${queryType}`);
        }
      } catch (queryError) {
        console.warn(`Query ${i + 1} failed:`, queryError);
      }
    }

    console.log(`Total documents processed: ${totalDocuments}`);
    const result = Array.from(communities).sort(); // Convert Set to sorted Array
    console.log(`Returning ${result.length} unique communities:`, result);

    return result;
  } catch (error) {
    console.error('Error fetching communities by pincode:', error);
    return [];
  }
};

/**
 * Debounced version of fetchCommunitiesByPincode to prevent excessive database queries
 */
export const debouncedFetchCommunitiesByPincode = debounce(fetchCommunitiesByPincode, 500);
