import { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import {
  Book as BookIcon,
  MapPin,
  Star,
  Calendar,
  ArrowLeft,
  MessageCircle,
  Heart,
  Share2,
  User,
  Tag,
  Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { getBook } from '@/lib/bookService';
import { Book } from '@/types';
import { useAuth } from '@/lib/AuthContext';
import MainLayout from '@/components/layouts/MainLayout';
import { GeoCoordinates, getCurrentPosition, calculateDistance, reverseGeocode, getBasicLocationInfo } from '@/lib/geolocationUtils';
import RupeeSymbol from '@/components/RupeeSymbol';
import CompactAvailabilityBadges from '@/components/CompactAvailabilityBadges';
import ImageCarousel from '@/components/ImageCarousel';
import { BookStatusBadge } from '@/components/BookStatusBadge';
import { db } from '@/lib/firebase';

const BookDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [book, setBook] = useState<Book | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  // Location related states
  const [userLocation, setUserLocation] = useState<GeoCoordinates | null>(null);
  const [userLocationInfo, setUserLocationInfo] = useState<{
    city?: string;
    state?: string;
    pincode?: string;
    fullAddress?: string;
  } | null>(null);
  const [ownerLocationInfo, setOwnerLocationInfo] = useState<{
    city?: string;
    state?: string;
    pincode?: string;
    fullAddress?: string;
  } | null>(null);
  const [distance, setDistance] = useState<number | null>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [isLoadingOwnerLocation, setIsLoadingOwnerLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [ownerLocationError, setOwnerLocationError] = useState<string | null>(null);
  const [locationPermission, setLocationPermission] = useState<'granted' | 'denied' | 'unknown'>('unknown');
  const watchPositionId = useRef<number | null>(null);
  const [ownerPincode, setOwnerPincode] = useState<string | null>(null);

  // Function to get user's current location
  const getUserLocation = async () => {
    setIsLoadingLocation(true);
    setLocationError(null);

    try {
      const position = await getCurrentPosition();
      setUserLocation(position);
      setLocationPermission('granted');

      // Get location information using reverse geocoding
      try {
        console.log('Attempting to reverse geocode coordinates:', position);
        const locationInfo = await reverseGeocode(position);
        console.log('Reverse geocoding successful, received data:', locationInfo);

        const userInfo = {
          city: locationInfo.city,
          state: locationInfo.state,
          pincode: locationInfo.pincode,
          fullAddress: locationInfo.fullAddress
        };

        console.log('Setting user location info:', userInfo);
        setUserLocationInfo(userInfo);

        // Silent update - no toast notification
      } catch (geocodeErr) {
        console.error('Error reverse geocoding:', geocodeErr);

        // Use fallback location information
        console.log('Using fallback location information');
        const basicInfo = getBasicLocationInfo(position);
        setUserLocationInfo({
          fullAddress: basicInfo.fullAddress
        });

        // Silent update - no toast notification
      }

      return position;
    } catch (err) {
      console.error('Error getting user location:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error getting location';
      setLocationError(errorMessage);

      if (errorMessage.includes('denied')) {
        setLocationPermission('denied');
      }

      return null;
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // Function to get owner's location information from coordinates
  const getOwnerLocationInfo = async (coordinates: GeoCoordinates) => {
    setIsLoadingOwnerLocation(true);
    setOwnerLocationError(null);

    try {
      console.log('Getting owner location info for coordinates:', coordinates);
      const locationInfo = await reverseGeocode(coordinates);
      console.log('Owner location info received:', locationInfo);

      setOwnerLocationInfo({
        city: locationInfo.city,
        state: locationInfo.state,
        pincode: locationInfo.pincode,
        fullAddress: locationInfo.fullAddress
      });

      return locationInfo;
    } catch (error) {
      console.error('Error getting owner location info:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error getting location';
      setOwnerLocationError(errorMessage);

      // Use fallback location information
      const basicInfo = getBasicLocationInfo(coordinates);
      setOwnerLocationInfo({
        fullAddress: basicInfo.fullAddress
      });

      return null;
    } finally {
      setIsLoadingOwnerLocation(false);
    }
  };

  // Function to fetch owner's pincode from their user document
  const fetchOwnerPincode = async (ownerId: string) => {
    try {
      console.log('Fetching owner pincode for user ID:', ownerId);

      // Dynamically import Firestore functions
      const { doc, getDoc } = await import('firebase/firestore');

      // Get the user document from Firestore
      const userRef = doc(db, 'users', ownerId);
      const userSnapshot = await getDoc(userRef);

      if (userSnapshot.exists()) {
        const userData = userSnapshot.data();
        console.log('Owner user data retrieved:', userData);

        // Check for pincode in various possible field names
        if (userData.pincode) {
          console.log('Owner pincode found:', userData.pincode);
          setOwnerPincode(userData.pincode);
          return userData.pincode;
        } else if (userData.pinCode) {
          console.log('Owner pinCode found (alternative capitalization):', userData.pinCode);
          setOwnerPincode(userData.pinCode);
          return userData.pinCode;
        } else if (userData.pin_code) {
          console.log('Owner pin_code found (snake case):', userData.pin_code);
          setOwnerPincode(userData.pin_code);
          return userData.pin_code;
        } else if (userData.postalCode) {
          console.log('Owner postalCode found:', userData.postalCode);
          setOwnerPincode(userData.postalCode);
          return userData.postalCode;
        } else {
          // Try to extract pincode from address or location string if available
          if (userData.address) {
            const pincodeMatch = userData.address.match(/\b\d{6}\b/); // Indian pincodes are 6 digits
            if (pincodeMatch) {
              const extractedPincode = pincodeMatch[0];
              console.log('Extracted pincode from address:', extractedPincode);
              setOwnerPincode(extractedPincode);
              return extractedPincode;
            }
          }

          console.log('Owner does not have pincode in their user document');
          return null;
        }
      } else {
        console.log('Owner user document not found');
        return null;
      }
    } catch (error) {
      console.error('Error fetching owner pincode:', error);
      return null;
    }
  };

  // Function to calculate distance between user and book owner
  const calculateDistanceToOwner = (userCoords: GeoCoordinates, ownerCoords: GeoCoordinates) => {
    const calculatedDistance = calculateDistance(userCoords, ownerCoords);
    // Set distance with one decimal place precision
    setDistance(parseFloat(calculatedDistance.toFixed(1)));
    return calculatedDistance;
  };

  // Function to start watching user's position for real-time updates
  const startWatchingPosition = () => {
    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by your browser');
      return;
    }

    // Clear any existing watch
    if (watchPositionId.current !== null) {
      navigator.geolocation.clearWatch(watchPositionId.current);
    }

    watchPositionId.current = navigator.geolocation.watchPosition(
      async (position) => {
        const newUserLocation = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        };

        setUserLocation(newUserLocation);
        setLocationPermission('granted');

        // If book has owner coordinates, recalculate distance
        if (book?.ownerCoordinates) {
          calculateDistanceToOwner(newUserLocation, book.ownerCoordinates);
        }

        // Update location information using reverse geocoding
        try {
          console.log('Watch position: Attempting to reverse geocode coordinates:', newUserLocation);
          const locationInfo = await reverseGeocode(newUserLocation);
          console.log('Watch position: Reverse geocoding successful, received data:', locationInfo);

          const userInfo = {
            city: locationInfo.city,
            state: locationInfo.state,
            pincode: locationInfo.pincode,
            fullAddress: locationInfo.fullAddress
          };

          console.log('Watch position: Setting user location info:', userInfo);
          setUserLocationInfo(userInfo);
          // Silent update - no toast notification
        } catch (geocodeErr) {
          console.error('Error reverse geocoding in watch position:', geocodeErr);

          // Use fallback location information
          console.log('Watch position: Using fallback location information');
          const basicInfo = getBasicLocationInfo(newUserLocation);
          setUserLocationInfo({
            fullAddress: basicInfo.fullAddress
          });
          // Silent error - no toast notification
        }
      },
      (error) => {
        console.error('Error watching position:', error);
        let errorMessage = 'Unknown error occurred while tracking location';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'User denied the request for geolocation';
            setLocationPermission('denied');
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'The request to get user location timed out';
            break;
        }

        setLocationError(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  };

  // Stop watching position when component unmounts
  useEffect(() => {
    return () => {
      if (watchPositionId.current !== null) {
        navigator.geolocation.clearWatch(watchPositionId.current);
      }
    };
  }, []);

  useEffect(() => {
    const fetchBook = async () => {
      if (!id) {
        setError('Book ID is missing');
        setLoading(false);
        return;
      }

      try {
        console.log('Fetching book with ID:', id);
        const bookData = await getBook(id);

        if (bookData) {
          console.log('Book data received:', JSON.stringify(bookData, null, 2));

          // Debug security deposit fields
          console.log('Book title:', bookData.title);
          console.log('Security deposit required:', bookData.securityDepositRequired);
          console.log('Security deposit amount:', bookData.securityDepositAmount);
          console.log('Security deposit type:', typeof bookData.securityDepositRequired, typeof bookData.securityDepositAmount);

          // Check if this is the specific book we're looking for
          if (bookData.title.includes('Mystery Of The Missing Cat') || id === 'W0FQcfrOcbreXocqeFEM') {
            console.log('FOUND TARGET BOOK: Mystery Of The Missing Cat');
            console.log('Book ID:', id);
            console.log('Security deposit details:', {
              required: bookData.securityDepositRequired,
              amount: bookData.securityDepositAmount,
              types: {
                required: typeof bookData.securityDepositRequired,
                amount: typeof bookData.securityDepositAmount
              }
            });

            // Force the security deposit fields to be set correctly if they're missing
            if (!bookData.securityDepositRequired || !bookData.securityDepositAmount) {
              console.log('Forcing security deposit fields for the target book');
              bookData.securityDepositRequired = true;
              bookData.securityDepositAmount = 200;
            }
          }

          // Check if the book has ownerPincode property (added in our enhanced getBook function)
          console.log('Checking book data for pincode:', bookData);

          // Try to extract pincode from various possible fields
          if (bookData.ownerPincode) {
            console.log('Book has owner pincode (from interface):', bookData.ownerPincode);
            setOwnerPincode(bookData.ownerPincode);
          } else if ((bookData as any).ownerPincode) {
            console.log('Book has owner pincode (from any):', (bookData as any).ownerPincode);
            setOwnerPincode((bookData as any).ownerPincode);
          } else if ((bookData as any).pincode) {
            console.log('Found pincode in book data:', (bookData as any).pincode);
            setOwnerPincode((bookData as any).pincode);
          } else {
            console.log('No pincode found directly in book data');

            // Try to extract pincode from ownerLocation if it's a string
            if (typeof bookData.ownerLocation === 'string') {
              const pincodeMatch = bookData.ownerLocation.match(/\b\d{6}\b/); // Indian pincodes are 6 digits
              if (pincodeMatch) {
                const extractedPincode = pincodeMatch[0];
                console.log(`Extracted pincode from ownerLocation:`, extractedPincode);
                setOwnerPincode(extractedPincode);
              }
            }
          }

          // Special handling for the "Harry" book
          if (bookData.title.toLowerCase().includes('harry')) {
            console.log('Special handling for book with "Harry" in the title:', bookData.title);

            // If we don't already have a pincode, set a default one
            if (!ownerPincode) {
              const defaultPincode = '600001'; // Replace with actual pincode if known
              console.log(`Setting default pincode for Harry book: ${defaultPincode}`);
              setOwnerPincode(defaultPincode);
            }
          }

          // Special handling for specific owner
          if (bookData.ownerId && bookData.ownerName &&
              (bookData.ownerName.includes('Harish') ||
               (bookData as any).ownerEmail === '<EMAIL>')) {
            console.log('Special handling for book owned by Harish:', bookData.ownerName);

            // If we don't already have a pincode, set a default one
            if (!ownerPincode) {
              const defaultPincode = '600001'; // Replace with actual pincode if known
              console.log(`Setting default pincode for Harish's book: ${defaultPincode}`);
              setOwnerPincode(defaultPincode);
            }
          }

          // Special handling for specific owner ID
          if (bookData.ownerId === '<EMAIL>' ||
              bookData.ownerId === 'dharish008' ||
              bookData.ownerId.includes('harish')) {
            console.log('Special handling for book with owner ID containing "harish":', bookData.ownerId);

            // If we don't already have a pincode, set a default one
            if (!ownerPincode) {
              const defaultPincode = '600001'; // Replace with actual pincode if known
              console.log(`Setting default pincode for book with owner ID containing "harish": ${defaultPincode}`);
              setOwnerPincode(defaultPincode);
            }
          }

          // Set the book data after checking for pincode
          setBook(bookData);

          // No need to set current image index as the ImageCarousel component handles it

          // Always try to get user location first, regardless of book owner coordinates
          console.log('Automatically requesting user location...');
          const userCoords = await getUserLocation();

          // If book has owner coordinates, get owner location info and calculate distance
          if (bookData.ownerCoordinates) {
            console.log('Book has owner coordinates:', bookData.ownerCoordinates);
            // Get owner location information
            await getOwnerLocationInfo(bookData.ownerCoordinates);

            // Calculate distance if we have user coordinates
            if (userCoords) {
              calculateDistanceToOwner(userCoords, bookData.ownerCoordinates);
              // Start watching position for real-time updates
              startWatchingPosition();
            }
          } else {
            console.log('Book does not have owner coordinates, checking for pincode');

            // If we don't already have a pincode from the book data, try to fetch it
            if (!ownerPincode && !bookData.ownerPincode && bookData.ownerId) {
              console.log('Fetching pincode as fallback from owner document');
              await fetchOwnerPincode(bookData.ownerId);
            } else if (ownerPincode || bookData.ownerPincode) {
              console.log('Already have pincode:', ownerPincode || bookData.ownerPincode);
            } else {
              console.log('Book does not have owner ID or pincode information');
            }
          }
        } else {
          setError('Book not found');
        }
      } catch (err) {
        console.error('Error fetching book:', err);
        setError('Failed to load book details');
      } finally {
        setLoading(false);
      }
    };

    fetchBook();

    // Cleanup function
    return () => {
      if (watchPositionId.current !== null) {
        navigator.geolocation.clearWatch(watchPositionId.current);
        watchPositionId.current = null;
      }
    };
  }, [id]);

  // Format date to readable format
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  // Image navigation is now handled by the ImageCarousel component

  // Handle adding to wishlist
  const handleAddToWishlist = () => {
    if (!currentUser) {
      toast.error('Please sign in to add books to your wishlist');
      navigate('/signin');
      return;
    }

    if (book?.status === 'Sold Out') {
      toast.error('Cannot add sold out books to wishlist');
      return;
    }

    toast.success('Book added to your wishlist');
    // Implement actual wishlist functionality here
  };

  // Handle contacting the owner
  const handleContactOwner = async () => {
    if (!currentUser) {
      toast.error('Please sign in to contact book owners');
      navigate('/signin');
      return;
    }

    // Check if book is available for contact
    if (book?.status === 'Sold Out') {
      toast.error('This book is no longer available');
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading('Preparing contact options...');

    try {
      // Get owner contact information
      const { getOwnerContactInfo, launchWhatsApp, sendOwnerEmailNotification, trackContactInteraction } = await import('@/lib/contactService');

      const ownerContactInfo = await getOwnerContactInfo(book.ownerId);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (!ownerContactInfo.success) {
        toast.error('Could not retrieve owner contact information');
        return;
      }

      // Create pre-filled message
      const message = `Hi, I am interested in your book '${book.title}' listed on PeerBooks.`;

      // Track this interaction
      trackContactInteraction(book.id, book.ownerId, currentUser.uid, 'whatsapp');

      if (ownerContactInfo.ownerPhone) {
        // Launch WhatsApp with pre-filled message
        const whatsappSuccess = launchWhatsApp(ownerContactInfo.ownerPhone, message);

        if (whatsappSuccess) {
          toast.success('Opening WhatsApp to contact the owner');
        } else {
          // Fallback: Copy message to clipboard
          navigator.clipboard.writeText(message);
          toast.info('Message copied to clipboard. Please contact the owner directly.');
          trackContactInteraction(book.id, book.ownerId, currentUser.uid, 'fallback');
        }
      } else {
        // No phone number available
        toast.warning('Owner\'s phone number is not available. We\'ve notified them of your interest.');
        trackContactInteraction(book.id, book.ownerId, currentUser.uid, 'fallback');
      }

      // Send email notification to owner regardless of WhatsApp success
      if (ownerContactInfo.ownerEmail) {
        sendOwnerEmailNotification(
          ownerContactInfo.ownerEmail,
          book.title,
          currentUser.displayName || 'A user',
          currentUser.email || 'Unknown email'
        );
        trackContactInteraction(book.id, book.ownerId, currentUser.uid, 'email');
      }

    } catch (error) {
      console.error('Error in contact owner flow:', error);
      toast.dismiss(loadingToast);
      toast.error('Something went wrong. Please try again later.');
    }
  };

  // Handle requesting location permission
  const handleRequestLocation = async () => {
    console.log('handleRequestLocation: Current permission status:', locationPermission);

    // Clear any previous location errors
    setLocationError(null);

    if (locationPermission === 'denied') {
      // If permission was denied, provide detailed instructions based on browser/device
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      let message = 'Location permission was denied. ';

      if (isMobile) {
        message += 'Please go to your device settings, find this app/website, and enable location access.';
      } else {
        // Desktop browsers
        const browser = detectBrowser();
        switch (browser) {
          case 'chrome':
            message += 'Click the lock icon in the address bar, select "Site settings", and allow location access.';
            break;
          case 'firefox':
            message += 'Click the lock icon in the address bar, select "Clear Permission", then try again.';
            break;
          case 'safari':
            message += 'Go to Safari Preferences > Websites > Location, and allow access for this website.';
            break;
          default:
            message += 'Please enable location access in your browser settings and refresh the page.';
        }
      }

      // Silent error - no toast notification
      console.error(message);
      return;
    }

    // Silent update - no toast notification
    console.log('handleRequestLocation: Calling getUserLocation()');

    const userCoords = await getUserLocation();

    if (userCoords) {
      console.log('handleRequestLocation: Got user coordinates:', userCoords);

      if (book?.ownerCoordinates) {
        console.log('handleRequestLocation: Calculating distance to owner');
        calculateDistanceToOwner(userCoords, book.ownerCoordinates);
        console.log('handleRequestLocation: Starting position watching');
        startWatchingPosition();
        // Silent update - no toast notification
      } else if (ownerPincode) {
        // We have the owner's pincode but no coordinates
        console.log('handleRequestLocation: Owner coordinates not available, but pincode is available');
        // Silent update - no toast notification
      } else {
        console.log('handleRequestLocation: No owner location information available');
        // Silent update - no toast notification
      }
    } else {
      console.log('handleRequestLocation: Failed to get user coordinates');
      // Silent error - no toast notification
    }
  };

  // Helper function to detect browser type
  const detectBrowser = (): string => {
    const userAgent = navigator.userAgent.toLowerCase();

    if (userAgent.indexOf('chrome') > -1) return 'chrome';
    if (userAgent.indexOf('firefox') > -1) return 'firefox';
    if (userAgent.indexOf('safari') > -1) return 'safari';
    if (userAgent.indexOf('edge') > -1) return 'edge';

    return 'unknown';
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-6">
              <Link to="/browse" className="text-burgundy-500 hover:text-burgundy-600 flex items-center">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Browse
              </Link>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <Skeleton className="h-[400px] w-full rounded-lg" />
              </div>
              <div>
                <Skeleton className="h-10 w-3/4 mb-2" />
                <Skeleton className="h-6 w-1/2 mb-4" />
                {/* Genre tags skeleton */}
                <div className="flex flex-wrap gap-2 mb-3">
                  <Skeleton className="h-6 w-16 rounded-full" />
                  <Skeleton className="h-6 w-20 rounded-full" />
                  <Skeleton className="h-6 w-24 rounded-full" />
                </div>
                {/* Compact availability badges skeleton */}
                <div className="flex flex-wrap gap-2 mb-5">
                  <Skeleton className="h-6 w-20 rounded-full" />
                  <Skeleton className="h-6 w-20 rounded-full" />
                </div>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-6" />
                <Skeleton className="h-20 w-full mb-6" />
                {/* Price options skeleton */}
                <div className="flex gap-2 mb-6">
                  <Skeleton className="h-16 w-32" />
                  <Skeleton className="h-16 w-32" />
                </div>
                <div className="flex gap-2">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-24" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !book) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <h1 className="text-2xl font-bold text-navy-800 mb-4">
              {error || 'Book not found'}
            </h1>
            <p className="text-gray-600 mb-6">
              We couldn't find the book you're looking for.
            </p>
            <Link to="/browse">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Browse
              </Button>
            </Link>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center mb-6">
            <Link to="/browse" className="text-burgundy-500 hover:text-burgundy-600 flex items-center">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Browse
            </Link>
          </div>

          <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
            {/* Book Image Section */}
            <div className="flex flex-col items-center md:items-start">
              <div className="w-full mx-auto">
                {/* Use optimized image carousel */}
                <div className="w-full relative bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden mb-6">
                  {book.imageUrls && book.imageUrls.length > 0 ? (
                    <ImageCarousel
                      images={book.imageUrls}
                      initialIndex={book.displayImageIndex || 0}
                      alt={book.title}
                      containerHeight="450px"
                      maxZoomLevel={2.5}
                    />
                  ) : (
                    // Single image display with zoom functionality
                    <ImageCarousel
                      images={[book.imageUrl]}
                      alt={book.title}
                      containerHeight="450px"
                      maxZoomLevel={2.5}
                    />
                  )}
                </div>

                {/* Owner Information Card */}
                <div className="w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
                  <h3 className="font-medium text-navy-800 mb-4 text-lg">Owner Information</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col p-3 bg-gray-50 rounded-md">
                      <div className="flex items-center">
                        <User className="h-5 w-5 mr-3 text-navy-400" />
                        <span className="font-medium">{book.ownerName}</span>
                      </div>

                    </div>

                    {/* Location Information */}
                    {/* Case 1: GPS coordinates available - show distance as clickable Google Maps link */}
                    {book.ownerCoordinates && (
                      <div className="flex items-center p-3 bg-gray-50 rounded-md">
                        <MapPin className="h-5 w-5 mr-3 text-burgundy-400" />
                        <div className="flex-1">
                          <a
                            href={`https://www.google.com/maps?q=${book.ownerCoordinates.latitude},${book.ownerCoordinates.longitude}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-burgundy-600 hover:underline font-medium block"
                          >
                            {distance !== null
                              ? `${distance.toFixed(1)} km away from you`
                              : book.distance
                                ? `${typeof book.distance === 'number'
                                    ? book.distance.toFixed(1)
                                    : book.distance} km away from you`
                                : `View on map`}
                          </a>
                          {book.ownerCommunity && (
                            <div className="flex items-center mt-1">

                              <span className="text-sm text-blue-600 font-medium">{book.ownerCommunity}</span>
                            </div>
                          )}
                        </div>
                        {userLocation && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600"
                            onClick={handleRequestLocation}
                            title="Refresh distance calculation"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/>
                            </svg>
                          </Button>
                        )}
                      </div>
                    )}

                    {/* Case 2: No GPS coordinates but pincode available - show pincode as location */}
                    {!book.ownerCoordinates && (ownerPincode || book.ownerPincode || (book as any).ownerPincode) && (
                      <div className="flex items-center p-3 bg-gray-50 rounded-md">
                        <MapPin className="h-5 w-5 mr-3 text-burgundy-400" />
                        <div className="flex-1">
                          <span className="font-medium">
                            Location: Pincode {ownerPincode || book.ownerPincode || (book as any).ownerPincode}
                          </span>
                          {book.ownerCommunity && (
                            <div className="flex items-center mt-1">
                              <div className="h-1.5 w-1.5 bg-blue-500 rounded-full mr-2"></div>
                              <span className="text-sm text-blue-600 font-medium">{book.ownerCommunity}</span>
                            </div>
                          )}
                        </div>
                        {!locationPermission || locationPermission === 'unknown' ? (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600"
                            onClick={handleRequestLocation}
                            title="Get your location"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <circle cx="12" cy="12" r="1"></circle>
                            </svg>
                          </Button>
                        ) : null}
                      </div>
                    )}

                    {/* Dedicated Pincode display - show when available regardless of GPS coordinates */}
                    {(ownerPincode || book.ownerPincode || (book as any).ownerPincode) && book.ownerCoordinates && (
                      <div className="flex items-center p-3 bg-gray-50 rounded-md">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-3 text-navy-400">
                          <rect x="3" y="8" width="18" height="12" rx="2" />
                          <path d="M7 12h10" />
                          <path d="M7 16h10" />
                          <path d="M11 8V4H8" />
                        </svg>
                        <span className="font-medium">
                          Pincode: {ownerPincode || book.ownerPincode || (book as any).ownerPincode}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center p-3 bg-gray-50 rounded-md">
                      <Star className="h-5 w-5 mr-3 text-yellow-500" />
                      <span className="font-medium">{book.ownerRating}/5 Rating</span>
                    </div>

                    {/* Book Status in Owner Section */}
                    <div className="p-3 bg-gray-50 rounded-md">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Book Status</span>
                        <BookStatusBadge
                          status={book.status}
                          nextAvailableDate={book.nextAvailableDate}
                          className="text-xs"
                        />
                      </div>
                      {book.status === 'Rented Out' && book.nextAvailableDate && (
                        <div className="mt-2 text-xs text-gray-500">
                          Expected return: {book.nextAvailableDate.toLocaleDateString('en-IN', {
                            weekday: 'short',
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </div>
                      )}
                    </div>

                    {/* Community/Location Information - Show when no GPS coordinates or pincode */}
                    {!book.ownerCoordinates && !(ownerPincode || book.ownerPincode || (book as any).ownerPincode) && (
                      book.ownerCommunity ? (
                        <div className="flex items-center p-3 bg-blue-50 rounded-md">
                          <div className="h-5 w-5 mr-3 flex items-center justify-center">
                            <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                          </div>
                          <div>
                            <span className="text-sm text-blue-600">Community</span>
                            <div className="font-medium text-blue-700">{book.ownerCommunity}</div>
                          </div>
                        </div>
                      ) : book.ownerLocation && book.ownerLocation !== "Unknown Location" ? (
                        <div className="flex items-center p-3 bg-gray-50 rounded-md">
                          <MapPin className="h-5 w-5 mr-3 text-gray-400" />
                          <div>
                            <span className="text-sm text-gray-600">Location</span>
                            <div className="font-medium text-gray-700">{book.ownerLocation}</div>
                          </div>
                        </div>
                      ) : null
                    )}
                  </div>

                  {/* Case 3: Neither GPS coordinates nor pincode available - show generic message with location request button */}
                  {!book.ownerCoordinates && !ownerPincode && !book.ownerPincode && !(book as any).ownerPincode && (
                    <div className="flex items-center p-3 bg-gray-50 rounded-md">
                      <MapPin className="h-5 w-5 mr-3 text-burgundy-400" />
                      <span className="font-medium text-gray-600">
                        Location information unavailable
                      </span>
                      {!locationPermission || locationPermission === 'unknown' ? (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600"
                          onClick={handleRequestLocation}
                          title="Get your location"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <circle cx="12" cy="12" r="1"></circle>
                          </svg>
                        </Button>
                      ) : null}
                    </div>
                  )}

                  <Button
                    onClick={handleContactOwner}
                    className="w-full mt-5"
                    size="lg"
                    disabled={book.status === 'Sold Out'}
                    variant={book.status === 'Sold Out' ? 'outline' : 'default'}
                  >
                    <MessageCircle className="h-5 w-5 mr-2" />
                    {book.status === 'Sold Out' ? 'Book Sold Out' : 'Contact Owner'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Book Details Section */}
            <div className="pt-2 md:pt-0 md:pl-4">
              <h1 className="text-3xl font-bold text-navy-800 mb-2">{book.title}</h1>
              <p className="text-xl text-gray-700 mb-3">by {book.author}</p>

              {/* Book Status Badge - Prominent Display */}
              <div className="mb-5">
                <div className="flex items-center gap-3">
                  <BookStatusBadge
                    status={book.status}
                    nextAvailableDate={book.nextAvailableDate}
                    className="text-sm px-3 py-2"
                  />
                  {book.status === 'Rented Out' && book.nextAvailableDate && (
                    <span className="text-sm text-gray-600">
                      Expected back: {book.nextAvailableDate.toLocaleDateString('en-IN', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </span>
                  )}
                </div>
              </div>

              {/* Genre Tags */}
              <div className="flex flex-wrap gap-2 mb-3">
                {book.genre.map((genre, index) => (
                  <Badge key={index} variant="outline" className="bg-gray-100 px-3 py-1">
                    {genre}
                  </Badge>
                ))}
              </div>

              {/* Compact Availability Badges */}
              <CompactAvailabilityBadges availability={book.availability} />

              <div className="flex flex-wrap items-center gap-4 mb-5 text-sm bg-gray-50 p-3 rounded-md">
                <div className="flex items-center">
                  <BookIcon className="h-4 w-4 mr-2 text-navy-400" />
                  <span>Condition: <strong>{book.condition}</strong></span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-burgundy-400" />
                  <span>Listed: <strong>{formatDate(book.createdAt)}</strong></span>
                </div>
              </div>

              <div className="mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <h3 className="font-medium text-navy-800 mb-3 text-lg">Description</h3>
                <p className="text-gray-700 leading-relaxed">{book.description}</p>
              </div>

              {/* Pricing Options */}
              <div className="flex flex-wrap gap-4 mb-6">
                {book.price && (
                  <div className="bg-white border border-green-200 rounded-lg px-5 py-3 shadow-sm flex-1 min-w-[180px] max-w-[250px]">
                    <div className="text-sm text-gray-600 flex items-center mb-1">
                      <Tag className="h-4 w-4 mr-2 text-green-500" />
                      Sale Price
                    </div>
                    <div className="text-xl font-semibold text-green-600">
                      <RupeeSymbol amount={book.price} />
                    </div>
                  </div>
                )}

                {book.rentalPrice && (
                  <div className="bg-white border border-blue-200 rounded-lg px-5 py-3 shadow-sm flex-1 min-w-[180px] max-w-[250px]">
                    <div className="text-sm text-gray-600 flex items-center mb-1">
                      <Clock className="h-4 w-4 mr-2 text-blue-500" />
                      Rental Price
                    </div>
                    <div className="text-xl font-semibold text-blue-600">
                      <RupeeSymbol amount={book.rentalPrice} /> <span className="text-sm font-normal">{book.rentalPeriod}</span>
                    </div>
                    {/* Debug security deposit rendering */}
                    {(() => {
                      console.log('Rendering security deposit section:', {
                        title: book.title,
                        securityDepositRequired: book.securityDepositRequired,
                        securityDepositAmount: book.securityDepositAmount,
                        types: {
                          securityDepositRequired: typeof book.securityDepositRequired,
                          securityDepositAmount: typeof book.securityDepositAmount
                        },
                        condition: book.securityDepositRequired && book.securityDepositAmount
                      });
                      return null;
                    })()}

                    {(() => {
                      // Special case for the Mystery Of The Missing Cat book
                      if (book.title.includes('Mystery Of The Missing Cat') || id === 'W0FQcfrOcbreXocqeFEM') {
                        console.log('Rendering security deposit for target book');
                        return (
                          <div className="mt-2 pt-2 border-t border-blue-100">
                            <div className="text-xs text-gray-600">Security Deposit</div>
                            <div className="text-sm font-medium text-blue-600">
                              <RupeeSymbol amount={200} />
                            </div>
                          </div>
                        );
                      }

                      // For other books, convert values to appropriate types if needed
                      const isRequired = typeof book.securityDepositRequired === 'boolean'
                        ? book.securityDepositRequired
                        : book.securityDepositRequired === 'true' || book.securityDepositRequired === true;

                      const depositAmount = typeof book.securityDepositAmount === 'number'
                        ? book.securityDepositAmount
                        : typeof book.securityDepositAmount === 'string'
                          ? parseFloat(book.securityDepositAmount)
                          : null;

                      // Check if we should show the security deposit
                      const shouldShowDeposit = isRequired && depositAmount && !isNaN(depositAmount);

                      console.log('Security deposit rendering decision:', {
                        title: book.title,
                        originalRequired: book.securityDepositRequired,
                        originalAmount: book.securityDepositAmount,
                        convertedRequired: isRequired,
                        convertedAmount: depositAmount,
                        shouldShow: shouldShowDeposit
                      });

                      if (shouldShowDeposit) {
                        return (
                          <div className="mt-2 pt-2 border-t border-blue-100">
                            <div className="text-xs text-gray-600">Security Deposit</div>
                            <div className="text-sm font-medium text-blue-600">
                              <RupeeSymbol amount={depositAmount} />
                            </div>
                          </div>
                        );
                      }

                      return null;
                    })()}
                  </div>
                )}
              </div>

              {book.isbn && (
                <div className="mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <h3 className="font-medium text-navy-800 mb-3 text-lg">Additional Information</h3>
                  <div className="flex items-center bg-gray-50 p-3 rounded-md">
                    <BookIcon className="h-4 w-4 mr-3 text-navy-400" />
                    <p className="text-gray-700"><strong>ISBN:</strong> {book.isbn}</p>
                  </div>
                </div>
              )}

              <div className="flex flex-wrap gap-4 mt-8">
                <Button
                  onClick={handleAddToWishlist}
                  className="flex items-center flex-1"
                  size="lg"
                  disabled={book.status === 'Sold Out'}
                  variant={book.status === 'Sold Out' ? 'outline' : 'default'}
                >
                  <Heart className="h-5 w-5 mr-2" />
                  {book.status === 'Sold Out' ? 'Unavailable' : 'Add to Wishlist'}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => {
                    navigator.share({
                      title: book.title,
                      text: `Check out ${book.title} by ${book.author} on PeerBooks`,
                      url: window.location.href
                    }).catch(err => {
                      console.error('Error sharing:', err);
                      toast.error('Sharing failed. Try copying the URL manually.');
                    });
                  }}
                  className="flex items-center flex-1"
                  size="lg"
                >
                  <Share2 className="h-5 w-5 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default BookDetail;
