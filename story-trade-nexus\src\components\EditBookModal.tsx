import React, { useState, useEffect } from 'react';
import { X, Save, Upload, Trash2, Calendar, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button-variants';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Book, BookEditFormData, BookCondition, BookAvailability, BookStatus } from '@/types';
import { updateBook } from '@/lib/bookService';
import { toast } from 'sonner';

interface EditBookModalProps {
  book: Book;
  isOpen: boolean;
  onClose: () => void;
  onBookUpdated: (updatedBook: Book) => void;
}

const bookConditions: BookCondition[] = ['New', 'Like New', 'Good', 'Fair'];
const bookAvailabilities: BookAvailability[] = [
  'For Rent',
  'For Exchange', 
  'For Sale',
  'For Rent & Sale',
  'For Rent & Exchange',
  'For Sale & Exchange',
  'For Rent, Sale & Exchange'
];
const bookStatuses: BookStatus[] = ['Available', 'Sold Out', 'Rented Out'];
const rentalPeriods = ['per day', 'per week', 'per month'];

const genres = [
  'Fiction', 'Non-Fiction', 'Mystery', 'Romance', 'Science Fiction', 'Fantasy',
  'Biography', 'History', 'Self-Help', 'Business', 'Technology', 'Health',
  'Travel', 'Cooking', 'Art', 'Religion', 'Philosophy', 'Poetry', 'Drama',
  'Children', 'Young Adult', 'Educational', 'Reference'
];

export const EditBookModal: React.FC<EditBookModalProps> = ({
  book,
  isOpen,
  onClose,
  onBookUpdated
}) => {
  const [formData, setFormData] = useState<BookEditFormData>({
    title: '',
    author: '',
    isbn: '',
    genre: [],
    condition: 'Good',
    description: '',
    availability: 'For Exchange',
    price: undefined,
    rentalPrice: undefined,
    rentalPeriod: 'per week',
    securityDepositRequired: false,
    securityDepositAmount: undefined,
    imageUrls: [],
    status: 'Available',
    nextAvailableDate: undefined
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);

  // Initialize form data when book changes
  useEffect(() => {
    if (book) {
      setFormData({
        title: book.title,
        author: book.author,
        isbn: book.isbn || '',
        genre: book.genre,
        condition: book.condition,
        description: book.description,
        availability: book.availability,
        price: book.price,
        rentalPrice: book.rentalPrice,
        rentalPeriod: book.rentalPeriod || 'per week',
        securityDepositRequired: book.securityDepositRequired || false,
        securityDepositAmount: book.securityDepositAmount,
        imageUrls: book.imageUrls || [book.imageUrl],
        status: book.status || 'Available',
        nextAvailableDate: book.nextAvailableDate
      });
      setSelectedGenres(book.genre);
    }
  }, [book]);

  const handleInputChange = (field: keyof BookEditFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleGenreToggle = (genre: string) => {
    const updatedGenres = selectedGenres.includes(genre)
      ? selectedGenres.filter(g => g !== genre)
      : [...selectedGenres, genre];
    
    setSelectedGenres(updatedGenres);
    handleInputChange('genre', updatedGenres);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.author.trim()) {
      toast.error('Title and author are required');
      return;
    }

    if (selectedGenres.length === 0) {
      toast.error('Please select at least one genre');
      return;
    }

    setIsLoading(true);
    
    try {
      // Prepare update data
      const updateData: Partial<Book> = {
        title: formData.title.trim(),
        author: formData.author.trim(),
        isbn: formData.isbn?.trim() || undefined,
        genre: selectedGenres,
        condition: formData.condition,
        description: formData.description.trim(),
        availability: formData.availability,
        price: formData.price,
        rentalPrice: formData.rentalPrice,
        rentalPeriod: formData.rentalPeriod,
        securityDepositRequired: formData.securityDepositRequired,
        securityDepositAmount: formData.securityDepositAmount,
        imageUrls: formData.imageUrls,
        status: formData.status,
        nextAvailableDate: formData.nextAvailableDate
      };

      // Update main image URL if imageUrls exist
      if (formData.imageUrls && formData.imageUrls.length > 0) {
        updateData.imageUrl = formData.imageUrls[0];
      }

      await updateBook(book.id, updateData);
      
      // Create updated book object for callback
      const updatedBook: Book = {
        ...book,
        ...updateData
      };
      
      onBookUpdated(updatedBook);
      toast.success('Book updated successfully');
      onClose();
    } catch (error) {
      console.error('Error updating book:', error);
      toast.error('Failed to update book');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-playfair font-bold text-navy-800">Edit Book</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
              disabled={isLoading}
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-4">
                {/* Title */}
                <div>
                  <Label htmlFor="title">Book Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter book title"
                    disabled={isLoading}
                    required
                  />
                </div>

                {/* Author */}
                <div>
                  <Label htmlFor="author">Author *</Label>
                  <Input
                    id="author"
                    value={formData.author}
                    onChange={(e) => handleInputChange('author', e.target.value)}
                    placeholder="Enter author name"
                    disabled={isLoading}
                    required
                  />
                </div>

                {/* ISBN */}
                <div>
                  <Label htmlFor="isbn">ISBN (Optional)</Label>
                  <Input
                    id="isbn"
                    value={formData.isbn}
                    onChange={(e) => handleInputChange('isbn', e.target.value)}
                    placeholder="Enter ISBN"
                    disabled={isLoading}
                  />
                </div>

                {/* Condition */}
                <div>
                  <Label htmlFor="condition">Condition *</Label>
                  <Select
                    value={formData.condition}
                    onValueChange={(value) => handleInputChange('condition', value as BookCondition)}
                    disabled={isLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select condition" />
                    </SelectTrigger>
                    <SelectContent>
                      {bookConditions.map((condition) => (
                        <SelectItem key={condition} value={condition}>
                          {condition}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Book Status */}
                <div>
                  <Label htmlFor="status">Current Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleInputChange('status', value as BookStatus)}
                    disabled={isLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {bookStatuses.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Next Available Date - only show if status is "Rented Out" */}
                {formData.status === 'Rented Out' && (
                  <div>
                    <Label htmlFor="nextAvailableDate">Expected Return Date</Label>
                    <Input
                      id="nextAvailableDate"
                      type="date"
                      value={formData.nextAvailableDate ? formData.nextAvailableDate.toISOString().split('T')[0] : ''}
                      onChange={(e) => handleInputChange('nextAvailableDate', e.target.value ? new Date(e.target.value) : undefined)}
                      disabled={isLoading}
                    />
                  </div>
                )}
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                {/* Availability */}
                <div>
                  <Label htmlFor="availability">Availability Type *</Label>
                  <Select
                    value={formData.availability}
                    onValueChange={(value) => handleInputChange('availability', value as BookAvailability)}
                    disabled={isLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select availability" />
                    </SelectTrigger>
                    <SelectContent>
                      {bookAvailabilities.map((availability) => (
                        <SelectItem key={availability} value={availability}>
                          {availability}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Price - show if availability includes "Sale" */}
                {formData.availability.includes('Sale') && (
                  <div>
                    <Label htmlFor="price">Sale Price (₹)</Label>
                    <Input
                      id="price"
                      type="number"
                      value={formData.price || ''}
                      onChange={(e) => handleInputChange('price', e.target.value ? Number(e.target.value) : undefined)}
                      placeholder="Enter sale price"
                      disabled={isLoading}
                    />
                  </div>
                )}

                {/* Rental Price - show if availability includes "Rent" */}
                {formData.availability.includes('Rent') && (
                  <>
                    <div>
                      <Label htmlFor="rentalPrice">Rental Price (₹)</Label>
                      <Input
                        id="rentalPrice"
                        type="number"
                        value={formData.rentalPrice || ''}
                        onChange={(e) => handleInputChange('rentalPrice', e.target.value ? Number(e.target.value) : undefined)}
                        placeholder="Enter rental price"
                        disabled={isLoading}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="rentalPeriod">Rental Period</Label>
                      <Select
                        value={formData.rentalPeriod}
                        onValueChange={(value) => handleInputChange('rentalPeriod', value)}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select period" />
                        </SelectTrigger>
                        <SelectContent>
                          {rentalPeriods.map((period) => (
                            <SelectItem key={period} value={period}>
                              {period}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Security Deposit */}
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="securityDeposit"
                        checked={formData.securityDepositRequired}
                        onCheckedChange={(checked) => handleInputChange('securityDepositRequired', checked)}
                        disabled={isLoading}
                      />
                      <Label htmlFor="securityDeposit">Require Security Deposit</Label>
                    </div>

                    {formData.securityDepositRequired && (
                      <div>
                        <Label htmlFor="securityDepositAmount">Security Deposit Amount (₹)</Label>
                        <Input
                          id="securityDepositAmount"
                          type="number"
                          value={formData.securityDepositAmount || ''}
                          onChange={(e) => handleInputChange('securityDepositAmount', e.target.value ? Number(e.target.value) : undefined)}
                          placeholder="Enter deposit amount"
                          disabled={isLoading}
                        />
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe the book's content, condition, and any other relevant details"
                rows={4}
                disabled={isLoading}
                required
              />
            </div>

            {/* Genres */}
            <div>
              <Label>Genres * (Select at least one)</Label>
              <div className="grid grid-cols-3 md:grid-cols-4 gap-2 mt-2">
                {genres.map((genre) => (
                  <div key={genre} className="flex items-center space-x-2">
                    <Checkbox
                      id={`genre-${genre}`}
                      checked={selectedGenres.includes(genre)}
                      onCheckedChange={() => handleGenreToggle(genre)}
                      disabled={isLoading}
                    />
                    <Label htmlFor={`genre-${genre}`} className="text-sm">
                      {genre}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Book
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
