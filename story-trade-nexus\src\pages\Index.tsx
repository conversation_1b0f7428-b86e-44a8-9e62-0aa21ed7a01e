import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import MainLayout from '@/components/layouts/MainLayout';
import HeroSection from '@/components/HeroSection';
import HowItWorks from '@/components/HowItWorks';
import FeaturedBooks from '@/components/FeaturedBooks';
import SearchResults from '@/components/SearchResults';
import { Book } from '@/types/index';
import { getAllBooks, searchBooks } from '@/lib/bookService';
import { Skeleton } from '@/components/ui/skeleton';
import { BooksGridLoader } from '@/components/PageLoader';
import { toast } from 'sonner';
import { useAuth } from '@/lib/AuthContext';

const Index = () => {
  const { userData } = useAuth();
  const [featuredBooks, setFeaturedBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<Book[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Fetch books from Firebase
  useEffect(() => {
    console.log('Home: Fetching books from Firebase for featured section');
    setLoading(true);

    const fetchBooks = async () => {
      try {
        // Get books from Firebase (only approved books)
        // Use community-priority sorting for featured books too
        const userCommunity = userData?.community;
        console.log('Home: User community for featured books sorting:', userCommunity);
        const books = await getAllBooks(false, userCommunity);

        if (books.length === 0) {
          console.log('No books found in Firestore for featured section');
          setFeaturedBooks([]);
          return;
        }

        // Books are already sorted by community priority + distance in getAllBooks function
        // Just take the first 8 books for the featured section
        const featured = books.slice(0, 8);
        console.log(`Home: Selected ${featured.length} books for featured section (sorted by community priority + distance)`);

        // Check if books have distance information
        const hasDistanceInfo = featured.some(book => book.distance !== undefined);
        const hasCommunityInfo = userCommunity && featured.some(book => book.ownerCommunity === userCommunity);

        if (hasDistanceInfo && hasCommunityInfo) {
          console.log(`Featured books are sorted by community (${userCommunity} first) and distance`);
        } else if (hasDistanceInfo) {
          console.log('Featured books are sorted by distance (closest first)');
        } else if (hasCommunityInfo) {
          console.log(`Featured books are sorted by community (${userCommunity} first) and newest first`);
        } else {
          console.log('Featured books are sorted by newest first (location not available)');
        }

        setFeaturedBooks(featured);
      } catch (error) {
        console.error('Error fetching featured books:', error);
        setFeaturedBooks([]); // Set empty array on error

        // Log detailed error information for debugging
        if (error instanceof Error) {
          console.error(`Featured books error details: ${error.message}`);
          if (error.stack) {
            console.error(`Stack trace: ${error.stack}`);
          }
        }
      } finally {
        setLoading(false);
      }
    };

    fetchBooks();
  }, []);

  // Handle search functionality
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      toast.error('Please enter a search term');
      return;
    }

    setSearchQuery(query);
    setIsSearching(true);

    try {
      console.log(`Performing search for: "${query}"`);
      const results = await searchBooks(query);
      setSearchResults(results);

      // Log search analytics
      console.log(`Search completed: "${query}" returned ${results.length} results`);
    } catch (error) {
      console.error('Search error:', error);
      toast.error('An error occurred while searching. Please try again.');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Clear search results and return to home page content
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
  };

  return (
    <MainLayout>
      <HeroSection onSearch={handleSearch} />

      {/* Conditionally render search results or home page content */}
      {searchQuery ? (
        <SearchResults
          results={searchResults}
          query={searchQuery}
          loading={isSearching}
          onClearSearch={clearSearch}
        />
      ) : (
        <>
          {loading ? (
            <section className="py-16 bg-white">
              <div className="container mx-auto px-4">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
                  <div>
                    <h2 className="text-3xl font-playfair font-bold text-navy-800 mb-2">Featured Books</h2>
                    <p className="text-gray-600">Explore our community's most interesting finds</p>
                  </div>
                </div>
                <BooksGridLoader count={8} />
              </div>
            </section>
          ) : (
            <FeaturedBooks books={featuredBooks} />
          )}
          <HowItWorks />
        </>
      )}

        {/* Testimonials Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-playfair font-bold text-navy-800 mb-3">What Our Community Says</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Hear from readers who've found their next favorite book through our platform.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Testimonial 1 */}
              <div className="bg-beige-100 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <div className="bg-burgundy-500 rounded-full w-10 h-10 flex items-center justify-center text-white font-medium">AP</div>
                  <div className="ml-3">
                    <h4 className="font-medium text-navy-800">Ankit Patel</h4>
                    <p className="text-sm text-gray-600">Mumbai</p>
                  </div>
                </div>
                <p className="text-gray-700 italic">
                  "I've exchanged over 15 books on PeerBooks and discovered authors I would have never found otherwise. The value-matching system makes exchanges so easy and fair!"
                </p>
              </div>

              {/* Testimonial 2 */}
              <div className="bg-beige-100 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <div className="bg-burgundy-500 rounded-full w-10 h-10 flex items-center justify-center text-white font-medium">SR</div>
                  <div className="ml-3">
                    <h4 className="font-medium text-navy-800">Sneha Reddy</h4>
                    <p className="text-sm text-gray-600">Bangalore</p>
                  </div>
                </div>
                <p className="text-gray-700 italic">
                  "As a student, I couldn't afford to buy all the books I wanted to read. PeerBooks has been a game changer - I can rent books affordably or exchange mine for new reads."
                </p>
              </div>

              {/* Testimonial 3 */}
              <div className="bg-beige-100 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <div className="bg-burgundy-500 rounded-full w-10 h-10 flex items-center justify-center text-white font-medium">RK</div>
                  <div className="ml-3">
                    <h4 className="font-medium text-navy-800">Rahul Khanna</h4>
                    <p className="text-sm text-gray-600">Delhi</p>
                  </div>
                </div>
                <p className="text-gray-700 italic">
                  "I love the community aspect of PeerBooks. I've met several fellow readers in my neighborhood and we now have regular book club meetups thanks to this platform!"
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-burgundy-500">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-playfair font-bold text-white mb-4">Ready to Join Our Community?</h2>
            <p className="text-beige-100 mb-8 max-w-2xl mx-auto">
              Start sharing your books with fellow readers and discover your next great read today.
            </p>
            <div className="flex flex-col md:flex-row justify-center space-y-4 md:space-y-0 md:space-x-4">
              <Link to="/join">
                <button className="bg-white text-burgundy-500 hover:bg-beige-100 font-medium px-6 py-3 rounded-md shadow-md transition-colors">
                  Sign Up Now
                </button>
              </Link>
              <Link to="/how-it-works">
                <button className="bg-transparent border border-white text-white hover:bg-burgundy-600 font-medium px-6 py-3 rounded-md shadow-md transition-colors">
                  Learn More
                </button>
              </Link>
            </div>
          </div>
        </section>
    </MainLayout>
  );
};

export default Index;
