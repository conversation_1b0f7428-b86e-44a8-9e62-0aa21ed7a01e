{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/context/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Context {\n  /**\n   * Get a value from the context.\n   *\n   * @param key key which identifies a context value\n   */\n  getValue(key: symbol): unknown;\n\n  /**\n   * Create a new context which inherits from this context and has\n   * the given key set to the given value.\n   *\n   * @param key context key for which to set the value\n   * @param value value to set for the given key\n   */\n  setValue(key: symbol, value: unknown): Context;\n\n  /**\n   * Return a new context which inherits from this context but does\n   * not contain a value for the given key.\n   *\n   * @param key context key for which to clear a value\n   */\n  deleteValue(key: symbol): Context;\n}\n\nexport interface ContextManager {\n  /**\n   * Get the current active context\n   */\n  active(): Context;\n\n  /**\n   * Run the fn callback with object set as the current active context\n   * @param context Any object to set as the current active context\n   * @param fn A callback to be immediately run within a specific context\n   * @param thisArg optional receiver to be used for calling fn\n   * @param args optional arguments forwarded to fn\n   */\n  with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F>;\n\n  /**\n   * Bind an object as the current context (or a specific one)\n   * @param [context] Optionally specify the context which you want to assign\n   * @param target Any object to which a context need to be set\n   */\n  bind<T>(context: Context, target: T): T;\n\n  /**\n   * Enable context management\n   */\n  enable(): this;\n\n  /**\n   * Disable context management\n   */\n  disable(): this;\n}\n"]}