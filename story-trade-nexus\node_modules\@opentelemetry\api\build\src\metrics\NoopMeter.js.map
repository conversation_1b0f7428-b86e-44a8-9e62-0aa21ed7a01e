{"version": 3, "file": "NoopMeter.js", "sourceRoot": "", "sources": ["../../../src/metrics/NoopMeter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAkBH;;;GAGG;AACH,MAAa,SAAS;IACpB,gBAAe,CAAC;IAEhB;;OAEG;IACH,WAAW,CAAC,KAAa,EAAE,QAAwB;QACjD,OAAO,yBAAiB,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,KAAa,EAAE,QAAwB;QACrD,OAAO,6BAAqB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,KAAa,EAAE,QAAwB;QACnD,OAAO,2BAAmB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,KAAa,EAAE,QAAwB;QACzD,OAAO,mCAA2B,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,qBAAqB,CACnB,KAAa,EACb,QAAwB;QAExB,OAAO,oCAA4B,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,uBAAuB,CACrB,KAAa,EACb,QAAwB;QAExB,OAAO,sCAA8B,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,6BAA6B,CAC3B,KAAa,EACb,QAAwB;QAExB,OAAO,8CAAsC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,0BAA0B,CACxB,SAAkC,EAClC,YAA0B,IACnB,CAAC;IAEV;;OAEG;IACH,6BAA6B,CAAC,SAAkC,IAAS,CAAC;CAC3E;AAzED,8BAyEC;AAED,MAAa,UAAU;CAAG;AAA1B,gCAA0B;AAE1B,MAAa,iBAAkB,SAAQ,UAAU;IAC/C,GAAG,CAAC,MAAc,EAAE,WAA6B,IAAS,CAAC;CAC5D;AAFD,8CAEC;AAED,MAAa,uBACX,SAAQ,UAAU;IAGlB,GAAG,CAAC,MAAc,EAAE,WAA6B,IAAS,CAAC;CAC5D;AALD,0DAKC;AAED,MAAa,eAAgB,SAAQ,UAAU;IAC7C,MAAM,CAAC,MAAc,EAAE,WAA6B,IAAS,CAAC;CAC/D;AAFD,0CAEC;AAED,MAAa,mBAAoB,SAAQ,UAAU;IACjD,MAAM,CAAC,MAAc,EAAE,WAA6B,IAAS,CAAC;CAC/D;AAFD,kDAEC;AAED,MAAa,oBAAoB;IAC/B,WAAW,CAAC,SAA6B,IAAG,CAAC;IAE7C,cAAc,CAAC,SAA6B,IAAG,CAAC;CACjD;AAJD,oDAIC;AAED,MAAa,2BACX,SAAQ,oBAAoB;CACG;AAFjC,kEAEiC;AAEjC,MAAa,yBACX,SAAQ,oBAAoB;CACC;AAF/B,8DAE+B;AAE/B,MAAa,iCACX,SAAQ,oBAAoB;CACS;AAFvC,8EAEuC;AAE1B,QAAA,UAAU,GAAG,IAAI,SAAS,EAAE,CAAC;AAE1C,0BAA0B;AACb,QAAA,mBAAmB,GAAG,IAAI,iBAAiB,EAAE,CAAC;AAC9C,QAAA,iBAAiB,GAAG,IAAI,eAAe,EAAE,CAAC;AAC1C,QAAA,qBAAqB,GAAG,IAAI,mBAAmB,EAAE,CAAC;AAClD,QAAA,2BAA2B,GAAG,IAAI,uBAAuB,EAAE,CAAC;AAEzE,2BAA2B;AACd,QAAA,8BAA8B,GAAG,IAAI,2BAA2B,EAAE,CAAC;AACnE,QAAA,4BAA4B,GAAG,IAAI,yBAAyB,EAAE,CAAC;AAC/D,QAAA,sCAAsC,GACjD,IAAI,iCAAiC,EAAE,CAAC;AAE1C;;GAEG;AACH,SAAgB,eAAe;IAC7B,OAAO,kBAAU,CAAC;AACpB,CAAC;AAFD,0CAEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Meter } from './Meter';\nimport {\n  BatchObservableCallback,\n  Counter,\n  Gauge,\n  Histogram,\n  MetricAttributes,\n  MetricOptions,\n  Observable,\n  ObservableCallback,\n  ObservableCounter,\n  ObservableGauge,\n  ObservableUpDownCounter,\n  UpDownCounter,\n} from './Metric';\n\n/**\n * NoopMeter is a noop implementation of the {@link Meter} interface. It reuses\n * constant NoopMetrics for all of its methods.\n */\nexport class NoopMeter implements Meter {\n  constructor() {}\n\n  /**\n   * @see {@link Meter.createGauge}\n   */\n  createGauge(_name: string, _options?: MetricOptions): Gauge {\n    return NOOP_GAUGE_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createHistogram}\n   */\n  createHistogram(_name: string, _options?: MetricOptions): Histogram {\n    return NOOP_HISTOGRAM_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createCounter}\n   */\n  createCounter(_name: string, _options?: MetricOptions): Counter {\n    return NOOP_COUNTER_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createUpDownCounter}\n   */\n  createUpDownCounter(_name: string, _options?: MetricOptions): UpDownCounter {\n    return NOOP_UP_DOWN_COUNTER_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createObservableGauge}\n   */\n  createObservableGauge(\n    _name: string,\n    _options?: MetricOptions\n  ): ObservableGauge {\n    return NOOP_OBSERVABLE_GAUGE_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createObservableCounter}\n   */\n  createObservableCounter(\n    _name: string,\n    _options?: MetricOptions\n  ): ObservableCounter {\n    return NOOP_OBSERVABLE_COUNTER_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createObservableUpDownCounter}\n   */\n  createObservableUpDownCounter(\n    _name: string,\n    _options?: MetricOptions\n  ): ObservableUpDownCounter {\n    return NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.addBatchObservableCallback}\n   */\n  addBatchObservableCallback(\n    _callback: BatchObservableCallback,\n    _observables: Observable[]\n  ): void {}\n\n  /**\n   * @see {@link Meter.removeBatchObservableCallback}\n   */\n  removeBatchObservableCallback(_callback: BatchObservableCallback): void {}\n}\n\nexport class NoopMetric {}\n\nexport class NoopCounterMetric extends NoopMetric implements Counter {\n  add(_value: number, _attributes: MetricAttributes): void {}\n}\n\nexport class NoopUpDownCounterMetric\n  extends NoopMetric\n  implements UpDownCounter\n{\n  add(_value: number, _attributes: MetricAttributes): void {}\n}\n\nexport class NoopGaugeMetric extends NoopMetric implements Gauge {\n  record(_value: number, _attributes: MetricAttributes): void {}\n}\n\nexport class NoopHistogramMetric extends NoopMetric implements Histogram {\n  record(_value: number, _attributes: MetricAttributes): void {}\n}\n\nexport class NoopObservableMetric {\n  addCallback(_callback: ObservableCallback) {}\n\n  removeCallback(_callback: ObservableCallback) {}\n}\n\nexport class NoopObservableCounterMetric\n  extends NoopObservableMetric\n  implements ObservableCounter {}\n\nexport class NoopObservableGaugeMetric\n  extends NoopObservableMetric\n  implements ObservableGauge {}\n\nexport class NoopObservableUpDownCounterMetric\n  extends NoopObservableMetric\n  implements ObservableUpDownCounter {}\n\nexport const NOOP_METER = new NoopMeter();\n\n// Synchronous instruments\nexport const NOOP_COUNTER_METRIC = new NoopCounterMetric();\nexport const NOOP_GAUGE_METRIC = new NoopGaugeMetric();\nexport const NOOP_HISTOGRAM_METRIC = new NoopHistogramMetric();\nexport const NOOP_UP_DOWN_COUNTER_METRIC = new NoopUpDownCounterMetric();\n\n// Asynchronous instruments\nexport const NOOP_OBSERVABLE_COUNTER_METRIC = new NoopObservableCounterMetric();\nexport const NOOP_OBSERVABLE_GAUGE_METRIC = new NoopObservableGaugeMetric();\nexport const NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC =\n  new NoopObservableUpDownCounterMetric();\n\n/**\n * Create a no-op Meter\n */\nexport function createNoopMeter(): Meter {\n  return NOOP_METER;\n}\n"]}