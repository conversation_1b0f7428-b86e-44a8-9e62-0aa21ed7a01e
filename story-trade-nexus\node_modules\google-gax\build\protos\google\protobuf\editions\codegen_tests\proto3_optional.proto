// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

syntax = "proto3";

package protobuf_editions_test.proto3;

message Proto3Optional {
  optional int32 int32_field = 17;
  optional float float_field = 18;
  optional double double_field = 19;
  optional int64 int64_field = 20;
  optional uint32 uint32_field = 21;
  optional uint64 uint64_field = 22;
  optional sint32 sint32_field = 23;
  optional sint64 sint64_field = 24;
  optional fixed32 fixed32_field = 25;
  optional fixed64 fixed64_field = 26;
  optional sfixed32 sfixed32_field = 27;
  optional sfixed64 sfixed64_field = 28;
  optional bool bool_field = 29;
  optional string string_field = 30;
  optional bytes bytes_field = 31;

  message SubMessage {
    optional int32 int32_field = 17;
    optional float float_field = 18;
    optional double double_field = 19;
    optional int64 int64_field = 20;
    optional uint32 uint32_field = 21;
    optional uint64 uint64_field = 22;
    optional sint32 sint32_field = 23;
    optional sint64 sint64_field = 24;
    optional fixed32 fixed32_field = 25;
    optional fixed64 fixed64_field = 26;
    optional sfixed32 sfixed32_field = 27;
    optional sfixed64 sfixed64_field = 28;
    optional bool bool_field = 29;
    optional string string_field = 30;
    optional bytes bytes_field = 31;
  }
  optional SubMessage optional_message = 2;
}
