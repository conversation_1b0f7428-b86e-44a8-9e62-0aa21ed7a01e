import{j as e,M as o,L as t}from"./index-Dkj_9ZOH.js";const s=()=>e.jsx(o,{children:e.jsx("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-navy-800 font-playfair mb-6",children:"Privacy Policy"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Effective Date: 23-05-2025"}),e.jsxs("div",{className:"prose prose-burgundy max-w-none",children:[e.jsx("p",{className:"mb-6",children:'PeerBooks ("we", "our", or "us") values your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our application and related services that utilize Facebook Login.'}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"1. Information We Collect"}),e.jsx("p",{children:"When you log in using Facebook, we may collect:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Your public profile (name, profile picture)"}),e.jsx("li",{children:"Your email address (if permitted by you)"})]}),e.jsx("p",{children:"We do not collect or store your Facebook password or sensitive personal data."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"2. How We Use Your Information"}),e.jsx("p",{children:"We use the collected information to:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Authenticate and log you into our platform"}),e.jsx("li",{children:"Personalize your user experience"}),e.jsx("li",{children:"Communicate important updates or changes"})]}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"3. Sharing of Your Information"}),e.jsx("p",{children:"We do not sell, rent, or share your data with third parties. Your data is used solely to provide services via PeerBooks."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"4. Data Retention"}),e.jsx("p",{children:"We retain your data only for as long as necessary to fulfill the purpose it was collected for, or as required by law."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"5. Data Security"}),e.jsx("p",{children:"We implement industry-standard security practices to protect your data. However, no method of transmission over the internet is completely secure."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"6. Your Rights"}),e.jsx("p",{children:"You may request to:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Access the data we have about you"}),e.jsx("li",{children:"Delete your data"}),e.jsx("li",{children:"Withdraw consent for future data use"})]}),e.jsxs("p",{children:["To do so, please visit our ",e.jsx(t,{to:"/data-deletion",className:"text-burgundy-500 hover:underline",children:"Data Deletion"}),' page or contact us using the information provided in the "Contact Us" section.']}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"7. Changes to This Policy"}),e.jsx("p",{children:"We may update this Privacy Policy periodically. Continued use of the app signifies acceptance of any changes."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"8. Contact Us"}),e.jsxs("p",{children:["If you have any questions about this Privacy Policy, please contact us at: ",e.jsx("a",{href:"mailto:<EMAIL>",className:"text-burgundy-500 hover:underline",children:"<EMAIL>"})]}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"9. User Data Deletion Instructions"}),e.jsxs("p",{children:["If you wish to delete your account and all associated personal data collected via Facebook and Google Login, please follow the instructions on our ",e.jsx(t,{to:"/data-deletion",className:"text-burgundy-500 hover:underline",children:"Data Deletion"})," page."]}),e.jsxs("p",{className:"mt-2",children:["In short, send an email to ",e.jsx("a",{href:"mailto:<EMAIL>",className:"text-burgundy-500 hover:underline",children:"<EMAIL>"}),' with the subject line "Delete My PeerBooks App Data" and include your name and the email associated with your account. Your data will be deleted within 48 hours, and you will receive a confirmation email once the deletion is complete.']}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"10. Additional Information for Social Login Users"}),e.jsx("p",{children:"When you use social login features (Google or Facebook), we only access the information you have explicitly allowed us to access through your privacy settings with those services. We do not request or store additional permissions beyond what is necessary for authentication and basic profile information."}),e.jsx("p",{children:"You can revoke our access to your social media accounts at any time through your social media account settings."})]})]})})});export{s as default};
