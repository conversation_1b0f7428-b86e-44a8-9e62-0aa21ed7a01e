import{u as E,r as o,J as d,f as R,j as e,H as $,S as G,I as P,h as m,i as f,k as w,l,m as I,n as M,L as Y,o as H}from"./index-Dkj_9ZOH.js";const Q=()=>{const{userData:t}=E(),[n,B]=o.useState(""),[x,S]=o.useState("All"),[u,A]=o.useState("All"),[b,F]=o.useState([]),[h,y]=o.useState(!0),[j,g]=o.useState(null),[c,i]=o.useState(null),C=["All","Fiction","Classics","Fantasy","Young Adult","Philosophy","Romance","Dystopian"],L=["All","For Rent","For Sale","For Exchange"];o.useEffect(()=>{p()},[]);const p=async()=>{try{y(!0),g(null),i("loading"),console.log("BrowseBooks: Fetching books from Firebase"),d.info("Getting your location to find nearby books...",{duration:3e3,id:"location-toast"});const s=t==null?void 0:t.community;console.log("BrowseBooks: User community for sorting:",s);const a=await R(!1,s);if(a.some(r=>r.distance!==void 0)){i("success");const r=s?`Books sorted by community (${s} first) and distance`:"Books sorted by distance (closest first)";d.success(r,{id:"location-toast",duration:4e3})}else{i("error");const r=s?`Books sorted by community (${s} first) and newest first`:"Books sorted by newest first";d.info(r,{id:"location-toast",duration:3e3})}F(a),console.log(`BrowseBooks: Fetched ${a.length} books from Firebase`)}catch(s){console.error("Error fetching books:",s),i("error"),s instanceof Error?(g(`Failed to load books: ${s.message}. Please try again.`),(s.message.includes("permission")||s.message.includes("denied"))&&(i("denied"),d.error("Location access denied. Books sorted by newest first.",{id:"location-toast",duration:5e3}))):g("Failed to load books. Please try again.")}finally{y(!1)}},v=()=>{console.log("BrowseBooks: Refreshing books"),p()},N=b.filter(s=>{const a=n===""||s.title.toLowerCase().includes(n.toLowerCase())||s.author.toLowerCase().includes(n.toLowerCase()),k=x==="All"||s.genre.includes(x),r=u==="All"||s.availability.includes(u);return a&&k&&r});return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx($,{}),e.jsx("main",{className:"flex-grow bg-beige-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Browse Books"}),e.jsx("p",{className:"text-gray-600",children:"Discover books available for exchange, rent, or purchase"})]}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(G,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500"}),e.jsx(P,{type:"text",placeholder:"Search by title or author...",className:"pl-10",value:n,onChange:s=>B(s.target.value)})]}),e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",value:x,onChange:s=>S(s.target.value),children:C.map(s=>e.jsx("option",{value:s,children:s},s))}),e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",value:u,onChange:s=>A(s.target.value),children:L.map(s=>e.jsx("option",{value:s,children:s},s))})]})}),e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[c==="loading"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(m,{className:"h-4 w-4 mr-1 text-gray-400 animate-pulse"}),e.jsx("span",{children:"Getting your location..."})]}),c==="success"&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(m,{className:"h-4 w-4 mr-1 text-green-500"}),e.jsx("span",{children:t!=null&&t.community?`Books sorted by community (${t.community} first) and distance`:"Books sorted by distance (closest first)"})]}),c==="error"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(m,{className:"h-4 w-4 mr-1 text-gray-400"}),e.jsx("span",{children:t!=null&&t.community?`Books sorted by community (${t.community} first) and newest first`:"Books sorted by newest first"})]}),c==="denied"&&e.jsxs("div",{className:"flex items-center text-sm text-amber-600",children:[e.jsx(m,{className:"h-4 w-4 mr-1 text-amber-500"}),e.jsx("span",{children:"Location access denied. Books sorted by newest first."})]})]}),e.jsx(f,{variant:"outline",onClick:v,disabled:h,className:"text-sm",children:h?e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"h-4 w-4 mr-2"}),"Refresh Books"]})})]}),j&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:j}),e.jsx(f,{variant:"link",onClick:v,className:"text-red-700 p-0 h-auto text-sm",children:"Try Again"})]}),h?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((s,a)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx(l,{className:"h-64 w-full"}),e.jsxs("div",{className:"p-4",children:[e.jsx(l,{className:"h-6 w-3/4 mb-2"}),e.jsx(l,{className:"h-4 w-1/2 mb-4"}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(l,{className:"h-8 w-20"}),e.jsx(l,{className:"h-8 w-20"})]})]})]},a))}):N.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:N.map(s=>e.jsx(I,{book:s},s.id))}):b.length===0?e.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[e.jsx(M,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Available Yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),e.jsx(Y,{to:"/add-books",children:e.jsx(f,{children:"Add Your Books"})})]}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found matching your criteria"}),e.jsx("p",{className:"text-burgundy-500",children:"Try adjusting your filters or search term"})]})]})}),e.jsx(H,{})]})};export{Q as default};
