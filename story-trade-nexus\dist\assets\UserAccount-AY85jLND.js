import{p as q,u as H,r as m,E as V,a1 as W,B as A,j as e,M as f,l as d,L as j,i as r,a2 as X,a3 as Z,a4 as G,a5 as K,a6 as w,c as Q,a7 as _,a8 as ee,m as B,X as se,s as ae,I as x,a9 as te,h as le,aa as re,g as de,J as u,ab as ie}from"./index-Dkj_9ZOH.js";import{P as y}from"./plus-CS8FKZGv.js";import{S as ne}from"./square-pen-BVD1bnhI.js";import{S as ce}from"./save-D3tQJMy_.js";import{P as oe}from"./phone-DG5weGza.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=q("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),ue=()=>{var S;const{currentUser:t,userData:s,refreshUserData:M,signOut:P}=H(),[c,E]=m.useState([]),[D,k]=m.useState(!0),[N,v]=m.useState(!1),[i,h]=m.useState("dashboard"),g=V(),C=W(),[n,b]=m.useState({displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:""});m.useEffect(()=>{const a=C.pathname;a.includes("profile")?h("profile"):a.includes("books")?h("books"):a.includes("settings")?h("settings"):h("dashboard")},[C.pathname]),m.useEffect(()=>{(async()=>{if(t)try{k(!0);const l=await de(t.uid);E(l),s&&b({displayName:s.displayName||"",phone:s.phone||"",address:s.address||"",apartment:s.apartment||"",city:s.city||"",state:s.state||"",pincode:s.pincode||""})}catch(l){console.error("Error fetching user data:",l),u.error("Failed to load profile data")}finally{k(!1)}})()},[t,s]);const I=a=>{if(!a)return"N/A";const l=a.toDate?a.toDate():new Date(a);return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(l)},o=a=>{const{name:l,value:R}=a.target;b(J=>({...J,[l]:R}))},L=async a=>{if(a.preventDefault(),!!t)try{await ie(t.uid,n),await M(),u.success("Profile updated successfully"),v(!1)}catch(l){console.error("Error updating profile:",l),u.error("Failed to update profile")}},F=()=>{s&&b({displayName:s.displayName||"",phone:s.phone||"",address:s.address||"",apartment:s.apartment||"",city:s.city||"",state:s.state||"",pincode:s.pincode||""}),v(!1)},p=a=>{switch(h(a),a){case"profile":g("/profile");break;case"books":g("/my-books");break;case"settings":g("/settings");break;default:g("/dashboard")}},Y=async()=>{try{await P(),g("/"),u.success("Signed out successfully")}catch(a){console.error("Error signing out:",a),u.error("Failed to sign out")}},O=()=>{var a;return s!=null&&s.displayName?s.displayName.split(" ").map(l=>l[0]).join("").toUpperCase().substring(0,2):((a=t==null?void 0:t.email)==null?void 0:a.substring(0,2).toUpperCase())||"U"},T=(s==null?void 0:s.displayName)||(t==null?void 0:t.displayName)||((S=t==null?void 0:t.email)==null?void 0:S.split("@")[0])||"Reader";s!=null&&s.email||t!=null&&t.email;const U=c.length||0,$=c.filter(a=>a.approvalStatus===A.Approved||!a.approvalStatus).length,z=c.filter(a=>a.approvalStatus===A.Pending).length;return D?e.jsx(f,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[e.jsxs("div",{className:"md:w-1/4",children:[e.jsx(d,{className:"h-40 w-40 rounded-full mx-auto"}),e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx(d,{className:"h-6 w-full"}),e.jsx(d,{className:"h-4 w-3/4"}),e.jsx(d,{className:"h-4 w-1/2"})]})]}),e.jsxs("div",{className:"md:w-3/4 space-y-6",children:[e.jsx(d,{className:"h-8 w-1/2"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(d,{className:"h-20 w-full"}),e.jsx(d,{className:"h-20 w-full"}),e.jsx(d,{className:"h-20 w-full"}),e.jsx(d,{className:"h-20 w-full"})]})]})]})})})}):!t||!s?e.jsx(f,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-4",children:"User Not Found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Please sign in to view your account."}),e.jsx(j,{to:"/signin",children:e.jsx(r,{children:"Sign In"})})]})})}):e.jsx(f,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsxs("div",{className:"flex flex-col md:flex-row",children:[e.jsxs("div",{className:"md:w-64 bg-gray-50 p-6 border-r border-gray-200",children:[e.jsxs("div",{className:"flex flex-col items-center mb-6",children:[e.jsxs(X,{className:"h-20 w-20",children:[e.jsx(Z,{src:s.photoURL||"",alt:s.displayName||"User"}),e.jsx(G,{className:"text-xl bg-burgundy-100 text-burgundy-700",children:O()})]}),e.jsx("h2",{className:"mt-4 text-lg font-semibold text-center",children:s.displayName}),e.jsx("p",{className:"text-sm text-gray-600 text-center",children:s.email})]}),e.jsxs("nav",{className:"space-y-1",children:[e.jsxs("button",{onClick:()=>p("dashboard"),className:`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${i==="dashboard"?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:[e.jsx(K,{className:"h-4 w-4 mr-3"}),"Dashboard"]}),e.jsxs("button",{onClick:()=>p("profile"),className:`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${i==="profile"?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:[e.jsx(w,{className:"h-4 w-4 mr-3"}),"Profile"]}),e.jsxs("button",{onClick:()=>p("books"),className:`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${i==="books"?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:[e.jsx(Q,{className:"h-4 w-4 mr-3"}),"My Books"]}),e.jsxs("button",{onClick:()=>p("settings"),className:`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${i==="settings"?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:[e.jsx(_,{className:"h-4 w-4 mr-3"}),"Settings"]})]}),e.jsx("div",{className:"mt-auto pt-6 border-t border-gray-200 mt-6",children:e.jsxs("button",{onClick:Y,className:"w-full flex items-center px-3 py-2 text-sm rounded-md text-gray-700 hover:bg-gray-100 transition-colors",children:[e.jsx(ee,{className:"h-4 w-4 mr-3"}),"Sign Out"]})})]}),e.jsxs("div",{className:"flex-1 p-6",children:[i==="dashboard"&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:["Welcome, ",T,"!"]}),e.jsx("p",{className:"text-gray-600",children:"Manage your books and exchanges"})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(j,{to:"/add-books",children:e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4"}),"Add New Books"]})})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[e.jsx("div",{className:"text-3xl font-bold text-burgundy-600",children:U}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Books"})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[e.jsx("div",{className:"text-3xl font-bold text-green-600",children:$}),e.jsx("div",{className:"text-sm text-gray-600",children:"Active Listings"})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[e.jsx("div",{className:"text-3xl font-bold text-amber-600",children:z}),e.jsx("div",{className:"text-sm text-gray-600",children:"Pending Approval"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-bold text-navy-800",children:"Your Books"}),e.jsx(r,{variant:"link",className:"text-burgundy-600",onClick:()=>p("books"),children:"View All"})]}),c.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:c.slice(0,4).map(a=>e.jsx(B,{book:a},a.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),e.jsx(j,{to:"/add-books",children:e.jsxs(r,{children:[e.jsx(y,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]})]}),i==="profile"&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Profile"}),N?e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(r,{variant:"outline",onClick:F,className:"flex items-center gap-2",children:[e.jsx(se,{className:"h-4 w-4"}),"Cancel"]}),e.jsxs(r,{onClick:L,className:"flex items-center gap-2",children:[e.jsx(ce,{className:"h-4 w-4"}),"Save Changes"]})]}):e.jsxs(r,{onClick:()=>v(!0),className:"flex items-center gap-2",children:[e.jsx(ne,{className:"h-4 w-4"}),"Edit Profile"]})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[e.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[e.jsx(w,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Personal Information"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(ae,{className:"h-4 w-4 text-gray-400 mr-2"}),e.jsx("p",{children:s.email})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(oe,{className:"h-4 w-4 text-gray-400 mr-2"}),N?e.jsx(x,{name:"phone",value:n.phone,onChange:o,placeholder:"Enter phone number"}):e.jsx("p",{children:s.phone||"Not provided"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Member Since"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(te,{className:"h-4 w-4 text-gray-400 mr-2"}),e.jsx("p",{children:I(s.createdAt)})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Display Name"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(w,{className:"h-4 w-4 text-gray-400 mr-2"}),N?e.jsx(x,{name:"displayName",value:n.displayName,onChange:o,placeholder:"Enter display name"}):e.jsx("p",{children:s.displayName||"Not provided"})]})]})]})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[e.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[e.jsx(le,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Address Information"]}),N?e.jsxs("form",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"text-sm text-gray-500",children:"Address"}),e.jsx(x,{name:"address",value:n.address,onChange:o,placeholder:"Enter your address",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"City"}),e.jsx(x,{name:"city",value:n.city,onChange:o,placeholder:"Enter city",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"State"}),e.jsx(x,{name:"state",value:n.state,onChange:o,placeholder:"Enter state",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"Pincode"}),e.jsx(x,{name:"pincode",value:n.pincode,onChange:o,placeholder:"Enter pincode",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"Apartment/Building"}),e.jsx(x,{name:"apartment",value:n.apartment,onChange:o,placeholder:"Enter apartment or building name",className:"mt-1"})]})]}):e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Address"}),e.jsxs("div",{className:"flex items-start mt-1",children:[e.jsx(re,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),e.jsx("p",{children:s.address||"Not provided"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"City"}),e.jsx("p",{className:"mt-1",children:s.city||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"State"}),e.jsx("p",{className:"mt-1",children:s.state||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Pincode"}),e.jsx("p",{className:"mt-1",children:s.pincode||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Apartment/Building"}),e.jsxs("div",{className:"flex items-start mt-1",children:[e.jsx(me,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),e.jsx("p",{children:s.apartment||"Not provided"})]})]})]})]})]}),i==="books"&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Books"}),e.jsx(j,{to:"/add-books",children:e.jsxs(r,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4"}),"Add New Book"]})})]}),c.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:c.map(a=>e.jsx(B,{book:a},a.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),e.jsx(j,{to:"/add-books",children:e.jsxs(r,{children:[e.jsx(y,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]}),i==="settings"&&e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-6",children:"Account Settings"}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Notification Preferences"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to customize your notification preferences here."})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Privacy Settings"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to manage your privacy settings here."})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4 text-red-600",children:"Danger Zone"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"These actions are irreversible. Please proceed with caution."}),e.jsx(r,{variant:"destructive",disabled:!0,children:"Delete Account"})]})]})]})]})})})})};export{ue as default};
