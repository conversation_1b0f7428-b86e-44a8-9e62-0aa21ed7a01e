import React from 'react';
import { Book as BookIcon, MapPin, Star, User, Home } from 'lucide-react';
import { Book } from '../types/index';
import { Badge } from '@/components/ui/badge';
import { BookStatusBadge } from '@/components/BookStatusBadge';
import { BookImage } from '@/components/LazyImage';
import { useAuth } from '@/lib/AuthContext';

interface BookCardProps {
  book: Book;
}

const BookCard: React.FC<BookCardProps> = ({ book }) => {
  const { userData } = useAuth();

  // Check if this book is from the same community as the current user
  const isSameCommunity = userData?.community && book.ownerCommunity &&
                         userData.community === book.ownerCommunity;

  const getAvailabilityBadge = (availability: string) => {
    switch (availability) {
      case 'For Rent':
        return <Badge className="bg-blue-500 hover:bg-blue-600">For Rent</Badge>;
      case 'For Sale':
        return <Badge className="bg-green-500 hover:bg-green-600">For Sale</Badge>;
      case 'For Exchange':
        return <Badge className="bg-purple-500 hover:bg-purple-600">For Exchange</Badge>;
      case 'For Rent & Sale':
        return (
          <div className="flex space-x-1">
            <Badge className="bg-blue-500 hover:bg-blue-600">For Rent</Badge>
            <Badge className="bg-green-500 hover:bg-green-600">For Sale</Badge>
          </div>
        );
      case 'For Rent & Exchange':
        return (
          <div className="flex space-x-1">
            <Badge className="bg-blue-500 hover:bg-blue-600">For Rent</Badge>
            <Badge className="bg-purple-500 hover:bg-purple-600">For Exchange</Badge>
            <Badge className="bg-purple-500 hover:bg-purple-600">For Exchange123</Badge>
          </div>
        );
      case 'For Sale & Exchange':
        return (
          <div className="flex space-x-1">
            <Badge className="bg-green-500 hover:bg-green-600">For Sale</Badge>
            <Badge className="bg-purple-500 hover:bg-purple-600">For Exchange</Badge>
          </div>
        );
      case 'For Rent, Sale & Exchange':
        return (
          <div className="flex space-x-1">
            <Badge className="bg-blue-500 hover:bg-blue-600">For Rent</Badge>
            <Badge className="bg-green-500 hover:bg-green-600">For Sale</Badge>
            <Badge className="bg-purple-500 hover:bg-purple-600">For Exchange</Badge>
          </div>
        );
      default:
        return null;
    }
  };

  // No need for a custom click handler anymore
  // The Link component will handle navigation

  return (
    <div onClick={() => window.location.href = `/books/${book.id}`} className="block cursor-pointer">
      <div className={`book-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-200 ${
        isSameCommunity ? 'ring-2 ring-blue-200 border-blue-300 bg-blue-50' : ''
      }`}>
        <div className="relative h-48 overflow-hidden">
          <BookImage
            src={book.imageUrl}
            book={{ title: book.title, author: book.author }}
            size="small"
            className="w-full h-full object-cover"
            loading="lazy"
          />
          <div className="absolute top-2 right-2 flex flex-col gap-1">
            <BookStatusBadge status={book.status} nextAvailableDate={book.nextAvailableDate} />
            {isSameCommunity && (
              <Badge className="bg-blue-500 hover:bg-blue-600 text-white text-xs flex items-center gap-1">
                <Home className="h-3 w-3" />
                Your Community
              </Badge>
            )}
          </div>
          <div className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/70 to-transparent p-3">
            <div className="text-white font-medium">
              {getAvailabilityBadge(book.availability)}
            </div>
          </div>
        </div>
        <div className="p-4">
          <h3 className="font-playfair font-medium text-lg text-navy-800 mb-1 line-clamp-1">{book.title}</h3>
          <p className="text-gray-600 text-sm mb-2">by {book.author}</p>

          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-700 flex items-center">
              <BookIcon className="h-3.5 w-3.5 mr-1" />
              {book.condition}
            </span>
            <span className="text-gray-700 flex items-center">
              <Star className="h-3.5 w-3.5 mr-1 text-yellow-500" />
              {book.ownerRating}
            </span>
          </div>

          <div className="mt-2 text-sm text-gray-600 flex items-center">
            <MapPin className="h-3.5 w-3.5 mr-1" />
            {book.distance !== undefined && book.distance !== null ? (
              <div className="flex items-center flex-wrap">
                <span className="font-medium text-burgundy-600">
                  {typeof book.distance === 'number' ? book.distance.toFixed(1) : book.distance} km away
                </span>
                {/* Show community with enhanced styling for same community */}
                {book.ownerCommunity ? (
                  <span className={`ml-1 font-medium flex items-center ${
                    isSameCommunity ? 'text-blue-700 font-bold' : 'text-blue-600'
                  }`}>
                    • {book.ownerCommunity}
                  </span>
                ) : book.ownerLocation && book.ownerLocation !== "Unknown Location" ? (
                  <span className="ml-1 text-gray-500">• {book.ownerLocation}</span>
                ) : null}
              </div>
            ) : book.ownerCoordinates ? (
              <div className="flex items-center flex-wrap">
                <span className="text-amber-600">Distance calculation pending</span>
                {book.ownerCommunity && (
                  <span className={`ml-1 font-medium flex items-center ${
                    isSameCommunity ? 'text-blue-700 font-bold' : 'text-blue-600'
                  }`}>
                    • {book.ownerCommunity}
                  </span>
                )}
              </div>
            ) : book.ownerCommunity ? (
              <div className="flex items-center">
                <span className={`font-medium ${
                  isSameCommunity ? 'text-blue-700 font-bold' : 'text-blue-600'
                }`}>
                  {book.ownerCommunity}
                </span>
              </div>
            ) : book.ownerLocation && book.ownerLocation !== "Unknown Location" ? (
              <span>{book.ownerLocation}</span>
            ) : book.ownerPincode ? (
              <span title="Exact location unavailable">
                Pincode: {book.ownerPincode}
              </span>
            ) : (
              <span>Location unavailable</span>
            )}
          </div>

          <div className="mt-2 pt-2 border-t border-gray-100">
            <div className="flex items-center text-sm text-gray-600 mb-2">
              <User className="h-3.5 w-3.5 mr-1" />
              <span>Owner: {book.ownerName}</span>
            </div>
          </div>

          <div className="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            {book.price && (
              <div className="text-burgundy-600 font-semibold">
                ₹{book.price}
              </div>
            )}
            {book.rentalPrice && (
              <div className="text-blue-600 font-semibold">
                ₹{book.rentalPrice} {book.rentalPeriod}
              </div>
            )}
            {!book.price && !book.rentalPrice && (
              <div className="text-purple-600 font-semibold">
                Exchange Only
              </div>
            )}
          </div>

        </div>
      </div>
    </div>
  );
};

export default BookCard;
